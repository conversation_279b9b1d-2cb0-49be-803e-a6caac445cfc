import { useState, useEffect } from "react";
import { safeIpcInvoke } from "../utils/electron";
import { Button } from "../components/button";
import { MinimalLoader } from "../components/LoadingSpinner";
import { useMasterDataAccess } from "../hooks/useRoleAccess";
import { RoleBasedComponent } from "../components/RoleBasedComponent";
import * as XLSX from "xlsx";
import {
  showErrorDialog,
  showWarningDialog,
  showInfoDialog,
  showSuccessNotification
} from "../utils/systemNotifications";

interface TransactionAdjustmentRequest {
  id: number;
  disputed_amount: number;
  disputed_full: number;
  disputed_type: string;
  currency_code: string;
  support_document: boolean;
  cdrs: string;
  card_type: string;
  reason: string;
  transaction_ref: string;
  transaction_report_detail_id: number;
  active: boolean;
  create_by: string;
  create_dt: string;
  update_by?: string;
  update_dt?: string;
}

interface AdjustmentResponse {
  success: boolean;
  message: string;
  data?: TransactionAdjustmentRequest | TransactionAdjustmentRequest[];
  pagination?: {
    currentPage: number;
    totalPages: number;
    totalRecords: number;
    pageSize: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
  error?: string;
}

interface PaginationParams {
  page?: number;
  pageSize?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: "ASC" | "DESC";
  startDate?: string;
  endDate?: string;
  disputedType?: string;
  transactionRef?: string;
  createBy?: string;
}

export function TransactionAdjustmentListScreen() {
  const { canExport } = useMasterDataAccess();
  const [adjustments, setAdjustments] = useState<TransactionAdjustmentRequest[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [totalRecords, setTotalRecords] = useState(0);
  const [pageSize, setPageSize] = useState(() => {
    const saved = localStorage.getItem("adjustmentList_pageSize");
    return saved ? parseInt(saved) : 10;
  });

  // Helper function to get default date range (1 week ago to today)
  const getDefaultDateRange = () => {
    const today = new Date();
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(today.getDate() - 30);
    
    return {
      startDate: oneWeekAgo.toISOString().split('T')[0],
      endDate: today.toISOString().split('T')[0]
    };
  };

  // Search and filter state
  const [searchTerm, setSearchTerm] = useState("");
  const [startDate, setStartDate] = useState(() => getDefaultDateRange().startDate);
  const [endDate, setEndDate] = useState(() => getDefaultDateRange().endDate);
  const [disputedType, setDisputedType] = useState("");
  const [transactionRef, setTransactionRef] = useState("");
  const [createBy, setCreateBy] = useState("");

  // Applied filter state
  const [appliedFilters, setAppliedFilters] = useState<PaginationParams>({});
  const [sortBy, setSortBy] = useState("create_dt");
  const [sortOrder, setSortOrder] = useState<"ASC" | "DESC">("DESC");
  const [isExporting, setIsExporting] = useState(false);

  // Reload adjustments when sorting changes
  useEffect(() => {
    if (Object.keys(appliedFilters).length > 0 && currentPage === 1) {
      loadAdjustments(1, pageSize, appliedFilters);
    }
  }, [sortBy, sortOrder, appliedFilters]);

  const loadAdjustments = async (
    page: number = currentPage,
    pageSizeParam: number = pageSize,
    appliedFiltersParam?: PaginationParams
  ) => {
    setIsLoading(true);
    try {
      if (
        appliedFiltersParam === undefined ||
        Object.keys(appliedFiltersParam).length === 0
      ) {
        setAdjustments([]);
        setCurrentPage(1);
        setTotalPages(0);
        setTotalRecords(0);
        setIsLoading(false);
        return;
      }

      const filtersToUse = appliedFiltersParam;

      const params: PaginationParams = {
        page,
        pageSize: pageSizeParam,
        sortBy,
        sortOrder,
        ...filtersToUse,
      };

      const response: AdjustmentResponse = await safeIpcInvoke(
        "get-transaction-adjustments-paginated",
        params
      );

      if (response.success && response.data && response.pagination) {
        setAdjustments(
          Array.isArray(response.data) ? response.data : [response.data]
        );
        setCurrentPage(response.pagination.currentPage);
        setTotalPages(response.pagination.totalPages);
        setTotalRecords(response.pagination.totalRecords);
        console.log(
          `✅ Loaded ${
            Array.isArray(response.data) ? response.data.length : 1
          } adjustment requests`
        );
      } else {
        console.error("❌ Failed to load adjustments:", response.message);
        setAdjustments([]);
        setTotalPages(0);
        setTotalRecords(0);
      }
    } catch (error) {
      console.error("❌ Error loading adjustments:", error);
      setAdjustments([]);
      setTotalPages(0);
      setTotalRecords(0);
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  };

  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat("th-TH", {
      style: "currency",
      currency: "THB",
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const getTypeBadge = (type: string) => {
    const typeColors = {
      "Credit Adjustment": "bg-green-100 text-green-800",
      "First Chargeback": "bg-red-100 text-red-800",
      "Debit Adjustment": "bg-orange-100 text-orange-800",
      "Representment": "bg-blue-100 text-blue-800",
      "Second Chargeback": "bg-purple-100 text-purple-800",
    };

    const colorClass =
      typeColors[type as keyof typeof typeColors] ||
      "bg-gray-100 text-gray-800";

    return (
      <span
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClass}`}
      >
        {type}
      </span>
    );
  };

  const getSupportDocBadge = (hasSupport: boolean) => {
    return (
      <span
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          hasSupport
            ? "bg-green-100 text-green-800"
            : "bg-gray-100 text-gray-800"
        }`}
      >
        {hasSupport ? "Yes" : "No"}
      </span>
    );
  };

  // Pagination functions
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
      loadAdjustments(newPage, pageSize, appliedFilters);
    }
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    localStorage.setItem("adjustmentList_pageSize", newPageSize.toString());
    setCurrentPage(1);
    loadAdjustments(1, newPageSize, appliedFilters);
  };

  const handleSort = (column: string) => {
    const newSortOrder =
      sortBy === column && sortOrder === "ASC" ? "DESC" : "ASC";

    setSortBy(column);
    setSortOrder(newSortOrder);
    setCurrentPage(1);
  };

  // Apply filters
  const applyFilters = () => {
    const newFilters: PaginationParams = {};

    if (searchTerm.trim()) newFilters.search = searchTerm.trim();
    if (startDate) newFilters.startDate = startDate;
    if (endDate) newFilters.endDate = endDate;
    if (disputedType) newFilters.disputedType = disputedType;
    if (transactionRef.trim()) newFilters.transactionRef = transactionRef.trim();
    if (createBy.trim()) newFilters.createBy = createBy.trim();

    setAppliedFilters(newFilters);
    setCurrentPage(1);
    loadAdjustments(1, pageSize, newFilters);
  };

  // Clear all filters
  const clearFilters = () => {
    const defaultDates = getDefaultDateRange();

    setSearchTerm("");
    setStartDate(defaultDates.startDate);
    setEndDate(defaultDates.endDate);
    setDisputedType("");
    setTransactionRef("");
    setCreateBy("");

    setAppliedFilters({});
    setCurrentPage(1);
    loadAdjustments(1, pageSize, {});
  };

  // Export to Excel
  const handleExport = async () => {
    if (adjustments.length === 0) {
      showWarningDialog("No data to export");
      return;
    }

    setIsExporting(true);
    try {
      // Get all data for export (not just current page)
      const exportParams: PaginationParams = {
        page: 1,
        pageSize: totalRecords, // Get all records
        sortBy,
        sortOrder,
        ...appliedFilters,
      };

      const response: AdjustmentResponse = await safeIpcInvoke(
        "get-transaction-adjustments-paginated",
        exportParams
      );

      if (response.success && response.data) {
        const exportData = Array.isArray(response.data) ? response.data : [response.data];

        // Prepare data for export
        const excelData = exportData.map((adjustment, index) => ({
          "No.": index + 1,
          "ID": adjustment.id,
          "Transaction Reference": adjustment.transaction_ref,
          "Disputed Amount": adjustment.disputed_amount,
          "Currency": adjustment.currency_code,
          "Dispute Type": adjustment.disputed_type,
          "Amount Type": adjustment.disputed_full === 1 ? "Full" : "Partial",
          "Support Documents": adjustment.support_document ? "Yes" : "No",
          "CDRS": adjustment.cdrs || "N/A",
          "Card Type": adjustment.card_type || "N/A",
          "Reason": adjustment.reason,
          "Created By": adjustment.create_by,
          "Created Date": formatDate(adjustment.create_dt),
          "Updated By": adjustment.update_by || "N/A",
          "Updated Date": adjustment.update_dt ? formatDate(adjustment.update_dt) : "N/A"
        }));

        // Create workbook and worksheet
        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.json_to_sheet(excelData);

        // Set column widths
        const colWidths = [
          { wch: 5 },   // No.
          { wch: 8 },   // ID
          { wch: 25 },  // Transaction Reference
          { wch: 15 },  // Disputed Amount
          { wch: 10 },  // Currency
          { wch: 20 },  // Dispute Type
          { wch: 12 },  // Amount Type
          { wch: 15 },  // Support Documents
          { wch: 12 },  // CDRS
          { wch: 15 },  // Card Type
          { wch: 30 },  // Reason
          { wch: 15 },  // Created By
          { wch: 20 },  // Created Date
          { wch: 15 },  // Updated By
          { wch: 20 },  // Updated Date
        ];
        ws['!cols'] = colWidths;

        // Add worksheet to workbook
        XLSX.utils.book_append_sheet(wb, ws, "Adjustment Requests");

        // Generate filename with timestamp
        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        const filename = `transaction_adjustment_requests_${timestamp}.xlsx`;

        // Write file
        XLSX.writeFile(wb, filename);

        showSuccessNotification(`Exported ${excelData.length} adjustment requests to ${filename}`);
      } else {
        showErrorDialog("Failed to export data: " + (response.message || "Unknown error"));
      }
    } catch (error) {
      console.error("Export error:", error);
      showErrorDialog("Failed to export data. Please try again.");
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <div className="flex flex-col gap-6 p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="flex flex-col items-center mb-4">
        <div className="flex items-center gap-4 mb-2">
          <h1 className="text-3xl font-bold text-center">
            Transaction Adjustment Requests
          </h1>
        </div>
        <p className="text-gray-600 text-center">
          View and manage all transaction adjustment requests with advanced filtering and pagination
        </p>
      </div>

      {/* Advanced Search and Filter Controls */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold mb-4 text-gray-900">
          Adjustment Request Filters
        </h3>

        {/* Row 1: Basic Search and Date Range */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Search
            </label>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Transaction Ref, Reason..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Start Date
            </label>
            <input
              type="date"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              End Date
            </label>
            <input
              type="date"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Dispute Type
            </label>
            <select
              value={disputedType}
              onChange={(e) => setDisputedType(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Types</option>
              <option value="Credit Adjustment">Credit Adjustment</option>
              <option value="First Chargeback">First Chargeback</option>
              <option value="Debit Adjustment">Debit Adjustment</option>
              <option value="Representment">Representment</option>
              <option value="Second Chargeback">Second Chargeback</option>
            </select>
          </div>
        </div>

        {/* Row 2: Additional Filters */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Transaction Reference
            </label>
            <input
              type="text"
              value={transactionRef}
              onChange={(e) => setTransactionRef(e.target.value)}
              placeholder="Transaction Reference"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Created By
            </label>
            <input
              type="text"
              value={createBy}
              onChange={(e) => setCreateBy(e.target.value)}
              placeholder="Username"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div> */}
        </div>

        {/* Filter Action Buttons */}
        <div className="flex flex-wrap gap-3">
          <Button
            onClick={applyFilters}
            variant="primary"
            className="flex items-center gap-2"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            Apply Filters
          </Button>

          <Button
            onClick={clearFilters}
            variant="secondary"
            className="flex items-center gap-2"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
            Clear Filters
          </Button>

          <RoleBasedComponent requiredPermissions={["export"]}>
            <Button
              onClick={handleExport}
              variant="secondary"
              disabled={isExporting || adjustments.length === 0}
              className="flex items-center gap-2"
            >
              {isExporting ? (
                <>
                  <MinimalLoader />
                  Exporting...
                </>
              ) : (
                <>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  Export Excel
                </>
              )}
            </Button>
          </RoleBasedComponent>
        </div>
      </div>

      

      {/* Main Content */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        {/* Table Header with Page Size Selector */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            Adjustment Requests
          </h3>
          <div className="flex items-center gap-2">
            <label className="text-sm text-gray-600">Show:</label>
            <select
              value={pageSize}
              onChange={(e) => handlePageSizeChange(parseInt(e.target.value))}
              className="px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value={10}>10</option>
              <option value={25}>25</option>
              <option value={50}>50</option>
              <option value={100}>100</option>
            </select>
            <span className="text-sm text-gray-600">per page</span>
          </div>
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="flex items-center justify-center py-12">
            <MinimalLoader/>
          </div>
        )}

        {/* Empty State */}
        {!isLoading && adjustments.length === 0 && Object.keys(appliedFilters).length === 0 && (
          <div className="text-center py-12">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No filters applied</h3>
            <p className="mt-1 text-sm text-gray-500">
              Apply filters to search for adjustment requests.
            </p>
          </div>
        )}

        {/* No Results State */}
        {!isLoading && adjustments.length === 0 && Object.keys(appliedFilters).length > 0 && (
          <div className="text-center py-12">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No adjustment requests found</h3>
            <p className="mt-1 text-sm text-gray-500">
              Try adjusting your search criteria or date range.
            </p>
          </div>
        )}

        {/* Table */}
        {!isLoading && adjustments.length > 0 && (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort("id")}
                  >
                    <div className="flex items-center gap-1">
                      ID
                      {sortBy === "id" && (
                        <svg className={`w-4 h-4 ${sortOrder === "ASC" ? "transform rotate-180" : ""}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      )}
                    </div>
                  </th>
                  <th
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort("transaction_ref")}
                  >
                    <div className="flex items-center gap-1">
                      Transaction Ref
                      {sortBy === "transaction_ref" && (
                        <svg className={`w-4 h-4 ${sortOrder === "ASC" ? "transform rotate-180" : ""}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      )}
                    </div>
                  </th>
                  <th
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort("disputed_amount")}
                  >
                    <div className="flex items-center gap-1">
                      Amount
                      {sortBy === "disputed_amount" && (
                        <svg className={`w-4 h-4 ${sortOrder === "ASC" ? "transform rotate-180" : ""}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      )}
                    </div>
                  </th>
                  <th
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort("disputed_type")}
                  >
                    <div className="flex items-center gap-1">
                      Type
                      {sortBy === "disputed_type" && (
                        <svg className={`w-4 h-4 ${sortOrder === "ASC" ? "transform rotate-180" : ""}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      )}
                    </div>
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Full/Partial
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Support Docs
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    CDRS
                  </th>
                  <th
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort("create_by")}
                  >
                    <div className="flex items-center gap-1">
                      Created By
                      {sortBy === "create_by" && (
                        <svg className={`w-4 h-4 ${sortOrder === "ASC" ? "transform rotate-180" : ""}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      )}
                    </div>
                  </th>
                  <th
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort("create_dt")}
                  >
                    <div className="flex items-center gap-1">
                      Created Date
                      {sortBy === "create_dt" && (
                        <svg className={`w-4 h-4 ${sortOrder === "ASC" ? "transform rotate-180" : ""}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      )}
                    </div>
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Reason
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {adjustments.map((adjustment) => (
                  <tr key={adjustment.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      #{adjustment.id}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-mono">
                      {adjustment.transaction_ref}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right font-medium">
                      {formatAmount(adjustment.disputed_amount)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getTypeBadge(adjustment.disputed_type)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        adjustment.disputed_full === 1
                          ? "bg-blue-100 text-blue-800"
                          : "bg-yellow-100 text-yellow-800"
                      }`}>
                        {adjustment.disputed_full === 1 ? "Full" : "Partial"}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getSupportDocBadge(adjustment.support_document)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {adjustment.cdrs || "N/A"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {adjustment.create_by}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(adjustment.create_dt)}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate" title={adjustment.reason}>
                      {adjustment.reason}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* Pagination */}
        {!isLoading && adjustments.length > 0 && totalPages > 1 && (
          <div className="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
            <div className="flex items-center justify-between">
              <div className="flex-1 flex justify-between sm:hidden">
                <Button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  variant="secondary"
                  size="sm"
                >
                  Previous
                </Button>
                <Button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  variant="secondary"
                  size="sm"
                >
                  Next
                </Button>
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    Showing{" "}
                    <span className="font-medium">
                      {(currentPage - 1) * pageSize + 1}
                    </span>{" "}
                    to{" "}
                    <span className="font-medium">
                      {Math.min(currentPage * pageSize, totalRecords)}
                    </span>{" "}
                    of <span className="font-medium">{totalRecords}</span> results
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                    <Button
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                      variant="secondary"
                      size="sm"
                      className="rounded-l-md"
                    >
                      Previous
                    </Button>

                    {/* Page numbers */}
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      let pageNum;
                      if (totalPages <= 5) {
                        pageNum = i + 1;
                      } else if (currentPage <= 3) {
                        pageNum = i + 1;
                      } else if (currentPage >= totalPages - 2) {
                        pageNum = totalPages - 4 + i;
                      } else {
                        pageNum = currentPage - 2 + i;
                      }

                      return (
                        <Button
                          key={pageNum}
                          onClick={() => handlePageChange(pageNum)}
                          variant={currentPage === pageNum ? "primary" : "secondary"}
                          size="sm"
                          className="border-l-0"
                        >
                          {pageNum}
                        </Button>
                      );
                    })}

                    <Button
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages}
                      variant="secondary"
                      size="sm"
                      className="rounded-r-md border-l-0"
                    >
                      Next
                    </Button>
                  </nav>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
