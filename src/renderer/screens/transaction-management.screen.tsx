import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useNotification } from '../contexts/NotificationContext';
import { safeIpcInvoke } from '../utils/electron';
import { Button } from '../components/button';
import { useMasterDataAccess } from '../hooks/useRoleAccess';
import { RoleBasedComponent } from '../components/RoleBasedComponent';


interface UploadedFile {
  fileName: string;
  filePath: string;
  size: number;
  uploadTime: Date;
  processed: boolean;
  error?: string;
  selected?: boolean;
  isDuplicate?: boolean;
  originalName?: string;
}







export function TransactionManagementScreen() {
  const { user } = useAuth();
  const { showNotification } = useNotification();
  const { canCreate, userRole, hasWriteAccess } = useMasterDataAccess();

  // File upload state
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingResult, setProcessingResult] = useState<any>(null);
  const [selectedFiles, setSelectedFiles] = useState<Set<string>>(new Set());

  // Date filter state - default to 1 week range
  const getDefaultStartDate = () => {
    const date = new Date();
    date.setDate(date.getDate() - 7); // 1 week ago
    return date.toISOString().split('T')[0];
  };

  const getDefaultEndDate = () => {
    return new Date().toISOString().split('T')[0]; // Today
  };

  const [startDate, setStartDate] = useState(getDefaultStartDate());
  const [endDate, setEndDate] = useState(getDefaultEndDate());

  const [error, setError] = useState<string>('');

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  };

  // Format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Validate file name format (must start with '1530')
  const validateFileName = (fileName: string): boolean => {
    return fileName.startsWith('1530');
  };

  // Check for duplicate file names and filter out duplicates and invalid names
  const filterDuplicates = (newFiles: File[], existingFiles: UploadedFile[]): { 
    validFiles: File[]; 
    duplicateFiles: File[]; 
    invalidFiles: File[] 
  } => {
    const existingNames = new Set(existingFiles.map(f => f.fileName));
    const currentBatchNames = new Set<string>();

    const validFiles: File[] = [];
    const duplicateFiles: File[] = [];
    const invalidFiles: File[] = [];

    newFiles.forEach(file => {
      // First check if file name format is valid
      if (!validateFileName(file.name)) {
        invalidFiles.push(file);
        return;
      }

      // Check if file name already exists in uploaded files or current batch
      if (existingNames.has(file.name) || currentBatchNames.has(file.name)) {
        duplicateFiles.push(file);
      } else {
        validFiles.push(file);
        currentBatchNames.add(file.name);
      }
    });

    return { validFiles, duplicateFiles, invalidFiles };
  };

  // Toggle file selection
  const toggleFileSelection = (fileName: string) => {
    setSelectedFiles(prev => {
      const newSet = new Set(prev);
      if (newSet.has(fileName)) {
        newSet.delete(fileName);
      } else {
        newSet.add(fileName);
      }
      return newSet;
    });
  };

  // Select all files
  const selectAllFiles = () => {
    const unprocessedFiles = uploadedFiles.filter(f => !f.processed);
    setSelectedFiles(new Set(unprocessedFiles.map(f => f.fileName)));
  };

  // Deselect all files
  const deselectAllFiles = () => {
    setSelectedFiles(new Set());
  };

  // Get selected file count
  const getSelectedCount = (): number => {
    return Array.from(selectedFiles).filter(fileName =>
      uploadedFiles.some(f => f.fileName === fileName && !f.processed)
    ).length;
  };

  // Handle drag and drop
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback(async (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    
    // Quick validation before processing
    const invalidFiles = files.filter(file => !validateFileName(file.name));
    if (invalidFiles.length === files.length) {
      showNotification(
        `All files rejected - file names must start with '1530'`,
        'error'
      );
      return;
    }
    
    await uploadFiles(files);
  }, []);

  // Handle file input
  const handleFileInput = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    await uploadFiles(files);
    // Reset the input so the same file can be selected again if needed
    e.target.value = '';
  }, []);

  // Upload files with duplicate detection (skip duplicates)
  const uploadFiles = async (files: File[]) => {
    if (!canCreate) {
      showNotification("You don't have permission to upload transaction files", "error");
      return;
    }

    if (files.length === 0) return;

    setIsUploading(true);
    setError('');

    try {
      // Filter out duplicate files and invalid file names
      const { validFiles, duplicateFiles, invalidFiles } = filterDuplicates(files, uploadedFiles);

      // Show error if invalid file names were found
      if (invalidFiles.length > 0) {
        const invalidNames = invalidFiles.map(f => f.name).join(', ');
        showNotification(
          `${invalidFiles.length} file(s) rejected - must start with '1530': ${invalidNames}`,
          'error'
        );
      }

      // Show warning if duplicates were found and skipped
      if (duplicateFiles.length > 0) {
        const duplicateNames = duplicateFiles.map(f => f.name).join(', ');
        showNotification(
          `${duplicateFiles.length} duplicate file(s) skipped: ${duplicateNames}`,
          'warning'
        );
      }

      // If no valid files to upload, return early
      if (validFiles.length === 0) {
        if (invalidFiles.length > 0 && duplicateFiles.length > 0) {
          showNotification('No files uploaded - all files were either invalid or duplicates', 'info');
        } else if (invalidFiles.length > 0) {
          showNotification('No files uploaded - all files have invalid names (must start with "1530")', 'info');
        } else if (duplicateFiles.length > 0) {
          showNotification('No new files to upload (all files were duplicates)', 'info');
        }
        return;
      }

      // Convert valid files to buffer format for IPC
      const fileData = await Promise.all(
        validFiles.map(async (file) => ({
          name: file.name,
          originalName: file.name,
          data: Buffer.from(await file.arrayBuffer()),
          isDuplicate: false
        }))
      );

      const result = await safeIpcInvoke('upload-transaction-files', fileData);

      if (result.success) {
        let message = `Successfully uploaded ${validFiles.length} files`;
        if (duplicateFiles.length > 0 || invalidFiles.length > 0) {
          const skippedCount = duplicateFiles.length + invalidFiles.length;
          message += ` (${skippedCount} files skipped)`;
        }
        showNotification(message, 'success');
        await fetchUploadedFiles();
      } else {
        setError(result.error || 'Upload failed');
        showNotification(result.error || 'Upload failed', 'error');
      }
    } catch (error) {
      console.error('Error uploading files:', error);
      setError('Failed to upload files');
      showNotification('Failed to upload files', 'error');
    } finally {
      setIsUploading(false);
    }
  };

  // Fetch uploaded files with date filtering
  const fetchUploadedFiles = async () => {
    try {
      const result = await safeIpcInvoke('get-uploaded-transaction-files');
      if (result.success) {
        // Filter files based on selected date range
        const filteredFiles = result.files.filter((file: UploadedFile) => {
          if (!startDate || !endDate) return true; // Show all if no date filter

          const fileDate = new Date(file.uploadTime).toISOString().split('T')[0];
          return fileDate >= startDate && fileDate <= endDate;
        });

        setUploadedFiles(filteredFiles);
      } else {
        console.error('Failed to fetch uploaded files:', result.error);
        showNotification(result.error || 'Failed to fetch uploaded files', 'error');
        setUploadedFiles([]); // Set empty array on error
      }
    } catch (error) {
      console.error('Error fetching uploaded files:', error);
      showNotification('Error fetching uploaded files', 'error');
      setUploadedFiles([]); // Set empty array on error
    }
  };

  // Process selected files
  const processFiles = async () => {
    if (!canCreate) {
      showNotification("You don't have permission to process transaction files", "error");
      return;
    }

    const selectedCount = getSelectedCount();
    if (selectedCount === 0) {
      showNotification('Please select at least one file to process', 'warning');
      return;
    }

    setIsProcessing(true);
    setError('');
    setProcessingResult(null);

    try {
      // Get list of selected file names
      const selectedFileNames = Array.from(selectedFiles).filter(fileName =>
        uploadedFiles.some(f => f.fileName === fileName && !f.processed)
      );

      const result = await safeIpcInvoke('process-transaction-files', {
        userName: user?.user_name || 'SYSTEM',
        selectedFiles: selectedFileNames
      });

      setProcessingResult(result);

      if (result.success) {
        // Enhanced notification with summary report information
        let message = `Processing complete: ${result.savedTransactions} transactions saved from ${selectedCount} files`;

        if (result.summaryReportId) {
          message += `. Summary report generated (ID: ${result.summaryReportId})`;
          if (result.merchantSummaryCount) {
            message += ` with ${result.merchantSummaryCount} merchant summaries`;
          }
        }

        showNotification(message, 'success');
        await fetchUploadedFiles();
        // Clear selections after successful processing
        setSelectedFiles(new Set());
      } else {
        setError(result.error || 'Processing failed');
        showNotification(result.error || 'Processing failed', 'error');
      }
    } catch (error) {
      console.error('Error processing files:', error);
      setError('Failed to process files');
      showNotification('Failed to process files', 'error');
    } finally {
      setIsProcessing(false);
    }
  };

  // Clear uploaded files
  const clearFiles = async () => {
    try {
      const result = await safeIpcInvoke('clear-uploaded-transaction-files');
      if (result.success) {
        setUploadedFiles([]);
        setProcessingResult(null);
        setSelectedFiles(new Set()); // Clear selections
        showNotification('Files cleared successfully', 'success');
      }
    } catch (error) {
      console.error('Error clearing files:', error);
      showNotification('Failed to clear files', 'error');
    }
  };

  // Load data on component mount
  useEffect(() => {
    fetchUploadedFiles();
  }, []);

  // Re-fetch files when date range changes
  useEffect(() => {
    fetchUploadedFiles();
  }, [startDate, endDate]);

  return (
    <div className="flex flex-col gap-6 p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="flex flex-col items-center mb-4">
        <h1 className="text-3xl font-bold text-center mb-2">📁 Upload Files</h1>
        <p className="text-gray-600 text-center">
          Upload and process transaction CSV/Excel files with automatic backup
        </p>
        {/* <p className="text-sm text-blue-600 mt-2">
          Role: {userRole?.toUpperCase()} | Access: {hasWriteAccess ? 'Read/Write' : 'Read Only'}
        </p> */}
      </div>


      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-red-700">{error}</p>
        </div>
      )}



      {/* File Upload Section */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-xl font-semibold mb-4">File Upload</h2>
        
        {/* Drag and Drop Area */}
        <div
          className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
            isDragOver 
              ? 'border-blue-500 bg-blue-50' 
              : 'border-gray-300 hover:border-gray-400'
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <div className="flex flex-col items-center gap-4">
            <div className="text-4xl text-gray-400">📁</div>
            <div>
              <p className="text-lg font-medium text-gray-700">
                Drag and drop upload files here
              </p>
              <p className="text-sm text-gray-500">
                Supports CSV and Excel files (.csv, .xlsx, .xls)
              </p>
              {/* <p className="text-sm text-orange-600 font-medium">
                File names must start with '1530'
              </p> */}
            </div>
            <div className="flex gap-2">
              <input
                type="file"
                multiple
                accept=".csv,.xlsx,.xls"
                onChange={handleFileInput}
                className="hidden"
                id="file-input"
              />
              <RoleBasedComponent
                requiredPermission="canCreate"
                fallback={
                  <span className="inline-flex items-center px-4 py-2 rounded-md text-sm font-medium bg-gray-300 text-gray-500 cursor-not-allowed">
                    Upload Disabled (View Only)
                  </span>
                }
              >
                <label htmlFor="file-input">
                  <span
                    className={`inline-flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors cursor-pointer ${
                      isUploading
                        ? 'bg-gray-400 text-gray-700 cursor-not-allowed'
                        : 'bg-blue-500 hover:bg-blue-600 text-white'
                    }`}
                  >
                    {isUploading ? 'Uploading...' : 'Choose Files'}
                  </span>
                </label>
              </RoleBasedComponent>
              {uploadedFiles.length > 0 && (
                <Button
                  onClick={clearFiles}
                  variant="secondary"
                  className="text-red-600 hover:text-red-700"
                >
                  Clear All
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* Uploaded Files List */}
        {uploadedFiles.length > 0 && (
          <div className="mt-6">
            <div className="flex justify-between items-center mb-4">
              <div className="flex items-center gap-4">
                <h3 className="text-lg font-medium">
                  Uploaded Files ({uploadedFiles.length})
                  {getSelectedCount() > 0 && (
                    <span className="text-sm text-blue-600 ml-2">
                      ({getSelectedCount()} selected)
                    </span>
                  )}
                </h3>

                {/* File Selection Controls */}
                {uploadedFiles.some(f => !f.processed) && (
                  <div className="flex gap-2">
                    <button
                      onClick={selectAllFiles}
                      className="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded hover:bg-blue-200"
                    >
                      Select All
                    </button>
                    <button
                      onClick={deselectAllFiles}
                      className="text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
                    >
                      Deselect All
                    </button>
                  </div>
                )}
              </div>

              <RoleBasedComponent
                requiredPermission="canCreate"
                fallback={
                  <span className="inline-flex items-center px-4 py-2 rounded-md text-sm font-medium bg-gray-300 text-gray-500 cursor-not-allowed">
                    Process Disabled (View Only)
                  </span>
                }
              >
                <Button
                  onClick={processFiles}
                  disabled={isProcessing || uploadedFiles.every(f => f.processed) || getSelectedCount() === 0}
                  className="bg-green-500 hover:bg-green-600 text-white"
                >
                  {isProcessing ? 'Processing...' : `Process ${getSelectedCount() > 0 ? `${getSelectedCount()} ` : ''}Files`}
                </Button>
              </RoleBasedComponent>
            </div>

            <div className="space-y-2">
              {uploadedFiles.map((file, index) => (
                <div
                  key={index}
                  className={`flex items-center justify-between p-3 rounded border ${
                    file.processed
                      ? 'bg-green-50 border-green-200'
                      : file.error
                        ? 'bg-red-50 border-red-200'
                        : selectedFiles.has(file.fileName)
                          ? 'bg-blue-50 border-blue-200'
                          : 'bg-gray-50 border-gray-200'
                  }`}
                >
                  <div className="flex items-center gap-3">
                    {/* Selection Checkbox */}
                    {!file.processed && (
                      <input
                        type="checkbox"
                        checked={selectedFiles.has(file.fileName)}
                        onChange={() => toggleFileSelection(file.fileName)}
                        className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                      />
                    )}

                    <div className="text-2xl">
                      {file.processed ? '✅' : file.error ? '❌' : file.isDuplicate ? '🔄' : '📄'}
                    </div>

                    <div>
                      <div className="flex items-center gap-2">
                        <p className="font-medium">{file.fileName}</p>
                        {file.isDuplicate && (
                          <span className="text-xs px-2 py-1 bg-yellow-100 text-yellow-800 rounded">
                            Renamed
                          </span>
                        )}
                      </div>
                      <p className="text-sm text-gray-500">
                        {formatFileSize(file.size)} • {formatDate(file.uploadTime.toString())}
                      </p>
                      {file.originalName && file.originalName !== file.fileName && (
                        <p className="text-xs text-yellow-600">
                          Original: {file.originalName}
                        </p>
                      )}
                      {file.error && (
                        <p className="text-sm text-red-600">{file.error}</p>
                      )}
                    </div>
                  </div>

                  <div className="text-sm">
                    {file.processed ? (
                      <span className="text-green-600 font-medium">Processed</span>
                    ) : file.error ? (
                      <span className="text-red-600 font-medium">Error</span>
                    ) : selectedFiles.has(file.fileName) ? (
                      <span className="text-blue-600 font-medium">Selected</span>
                    ) : (
                      <span className="text-gray-500">Pending</span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Processing Result */}
        {processingResult && (
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <h3 className="font-medium mb-2">Processing Results</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <p className="text-gray-600">Files Processed</p>
                <p className="font-bold">{processingResult.processedFiles}/{processingResult.totalFiles}</p>
              </div>
              <div>
                <p className="text-gray-600">Transactions Saved</p>
                <p className="font-bold text-green-600">{processingResult.savedTransactions}</p>
              </div>
              <div>
                <p className="text-gray-600">pCloud Backups</p>
                <p className="font-bold text-blue-600">
                  {processingResult.backupResults?.filter((r: any) => r.success).length || 0}
                </p>
              </div>
              {processingResult.summaryReportId && (
                <div>
                  <p className="text-gray-600">Summary Report</p>
                  <p className="font-bold text-green-600">Generated</p>
                  <p className="text-xs text-gray-500">ID: {processingResult.summaryReportId}</p>
                </div>
              )}
              {processingResult.merchantSummaryCount && (
                <div>
                  <p className="text-gray-600">Merchant Summaries</p>
                  <p className="font-bold text-purple-600">{processingResult.merchantSummaryCount}</p>
                  <p className="text-xs text-gray-500">Groups created</p>
                </div>
              )}
            </div>
            {processingResult.errors?.length > 0 && (
              <div className="mt-3">
                <p className="text-red-600 font-medium">Errors:</p>
                <ul className="text-sm text-red-600 list-disc list-inside">
                  {processingResult.errors.slice(0, 5).map((error: string, index: number) => (
                    <li key={index}>{error}</li>
                  ))}
                  {processingResult.errors.length > 5 && (
                    <li>... and {processingResult.errors.length - 5} more errors</li>
                  )}
                </ul>
              </div>
            )}
          </div>
        )}
      </div>





      {/* Instructions */}
      {/* <div className="bg-blue-50 rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4">📋 How to Upload Transaction Files</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="font-semibold mb-2">File Upload Process:</h3>
            <ol className="list-decimal list-inside space-y-1 text-sm">
              <li>📁 Drag and drop CSV/Excel files or click "Choose Files"</li>
              <li>🔄 Duplicate file names are automatically renamed</li>
              <li>📋 Review uploaded files list and select files to process</li>
              <li>✅ Use checkboxes to select specific files or "Select All"</li>
              <li>🔄 Click "Process Files" to process selected files only</li>
              <li>☁️ Files are automatically backed up to pCloud</li>
              <li>💾 Transactions are saved to database</li>
            </ol>

            <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded">
              <p className="text-xs text-yellow-800">
                <strong>💡 Tip:</strong> Files with duplicate names will be automatically renamed (e.g., "file.csv" → "file_1.csv")
                to prevent conflicts. Original names are preserved for reference.
              </p>
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-2">Automatic Workflow:</h3>
            <ol className="list-decimal list-inside space-y-1 text-sm">
              <li>📄 Parse CSV/Excel file structure</li>
              <li>🔍 Lookup merchant information</li>
              <li>📅 Backup to pCloud with date folder (csv_backup/YYYY-MM-DD)</li>
              <li>💾 Save transactions to database</li>
            </ol>
          </div>
        </div>

        <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
          <h3 className="font-semibold text-yellow-800 mb-2">💡 Pro Tips:</h3>
          <ul className="list-disc list-inside space-y-1 text-sm text-yellow-700">
            <li>Supported file formats: CSV, Excel (.xlsx, .xls)</li>
            <li>Files are automatically backed up to pCloud in date-based folders</li>
            <li>All transactions from CSV files are processed and inserted, including duplicates</li>
            <li>Processed transactions can be viewed in the Transaction List page</li>
            <li>Merchant information is automatically looked up from the merchant database</li>
          </ul>
        </div>
      </div> */}
    </div>
  );
}
