import { useState, useEffect, useRef, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '../components/button';
import { useAuth } from '../contexts/AuthContext';
import { useNotification } from '../contexts/NotificationContext';
import { safeIpcInvoke } from '../utils/electron';
import { RoleBasedComponent } from '../components/RoleBasedComponent';
import { formatDateYearMonthDay } from 'renderer/utils/helper';
import TransactionLogSearchModal from '../components/TransactionLogSearchModal';
// Types for Transaction Summary using only detail table
interface TransactionSummaryDetailItem {
  id: number;
  merchant_vat: string;
  merchant_name: string;
  transaction_date: string;
  channel_type: string;
  transaction_count: number;
  total_amount: number;
  mdr_rate: number;
  mdr_amount: number;
  vat_percentage: number;
  vat_amount: number;
  net_amount: number;
  withholding_tax_rate: number;
  withhold_tax: number;
  transfer_fee: number;
  reimbursement_fee: number;
  service_fee: number;
  final_net_amount: number;
  cup_business_tax_fee: number;
  is_transfer: number;
  create_by: string;
  create_dt: string;
  update_by: string;
  update_dt: string;
  // New bank-related fields
  bank_id?: number;
  bank_account_no?: string;
  bank_code?: string;
  bank_name_th?: string;
  bank_name_en?: string;
  merchant_ref?: string; // New field for merchant reference
  trx_no?: string; // New field for transaction number
  trade_status?: string; // New field for trade status
}

// Interface for merchant summary
interface MerchantSummary {
  merchant_vat: string;
  merchant_name: string;
  total_transactions: number;
  total_amount: number;
  transferred_amount: number;
  pending_amount: number;
  transferred_count: number;
  pending_count: number;
  // New bank-related fields
  bank_id?: number;
  bank_account_no?: string;
  bank_code?: string;
  bank_name_th?: string;
  bank_name_en?: string;
  merchant_ref?: string; // New field for merchant reference
  trx_no?: string; // New field for transaction number
}

// Interface for column configuration
interface ColumnConfig {
  key: string;
  label: string;
  minWidth: number;
  defaultWidth: number;
}

// Column definitions for resizable table
const tableColumns: ColumnConfig[] = [
  { key: 'select', label: '', minWidth: 60, defaultWidth: 80 },
  { key: 'date', label: 'Date', minWidth: 100, defaultWidth: 120 },
  { key: 'merchant_vat', label: 'Merchant VAT', minWidth: 120, defaultWidth: 140 },
  { key: 'merchant_name', label: 'Merchant Name', minWidth: 150, defaultWidth: 180 },
  { key: 'merchant_ref', label: 'Merchant Ref', minWidth: 120, defaultWidth: 140 },
  { key: 'bank_account_no', label: 'Account Bank No', minWidth: 120, defaultWidth: 140 },
  { key: 'bank_name', label: 'Bank Name', minWidth: 150, defaultWidth: 180 },
  { key: 'mdr_rate', label: 'MDR %', minWidth: 80, defaultWidth: 100 },
  { key: 'transaction_count', label: 'No Of TXN', minWidth: 90, defaultWidth: 110 },
  { key: 'total_amount', label: 'Trx Amt', minWidth: 100, defaultWidth: 120 },
  { key: 'mdr_amount', label: 'MDR Amt', minWidth: 100, defaultWidth: 120 },
  { key: 'transfer_fee', label: 'Transfer Fee', minWidth: 100, defaultWidth: 120 },
  { key: 'vat_amount', label: 'Vat Amt', minWidth: 100, defaultWidth: 120 },
  { key: 'net_amount', label: 'Net Amt', minWidth: 100, defaultWidth: 120 },
  { key: 'withhold_tax', label: 'Withhold Tax', minWidth: 110, defaultWidth: 130 },
  { key: 'reimbursement_fee', label: 'Reimburse Fee', minWidth: 110, defaultWidth: 130 },
  { key: 'service_fee', label: 'Service Fee', minWidth: 100, defaultWidth: 120 },
  { key: 'cup_business_tax_fee', label: 'Business Fee', minWidth: 110, defaultWidth: 130 },
  { key: 'final_net_amount', label: 'Net Amt CUP', minWidth: 110, defaultWidth: 130 },
  { key: 'channel_type', label: 'Provider', minWidth: 100, defaultWidth: 120 },
  { key: 'transfer_status', label: 'Transfer Status', minWidth: 120, defaultWidth: 140 },
  // { key: 'actions', label: 'Actions', minWidth: 120, defaultWidth: 140 },
];

// Resizable Header Component
interface ResizableHeaderProps {
  column: ColumnConfig;
  width: number;
  onMouseDown: (e: React.MouseEvent, columnKey: string) => void;
  isLoading?: boolean;
  children: React.ReactNode;
  className?: string;
}

const ResizableHeader: React.FC<ResizableHeaderProps> = ({ 
  column, 
  width, 
  onMouseDown, 
  isLoading, 
  children, 
  className = '' 
}) => {
  const isOrange = className.includes('orange');
  
  return (
    <th 
      className={`relative text-center text-xs font-medium uppercase tracking-wider select-none ${className}`}
      style={{ 
        width: `${width}px`, 
        minWidth: `${column.minWidth}px`,
        maxWidth: `${width}px`,
        padding: '12px 8px',
        position: 'relative'
      }}
    >
      {isLoading ? (
        <div className="animate-pulse bg-gray-200 h-4 rounded mx-2"></div>
      ) : (
        <div className="truncate" style={{ maxWidth: `${width - 16}px` }}>
          {children}
        </div>
      )}
      {!isLoading && (
        <div
          className={`resize-handle ${isOrange ? 'orange' : ''}`}
          onMouseDown={(e) => {
            console.log('🔥 RESIZE HANDLE CLICKED for column:', column.key);
            console.log('🔥 Event details:', { clientX: e.clientX, target: e.target });
            e.preventDefault();
            e.stopPropagation();
            onMouseDown(e, column.key);
          }}
          title="Drag to resize column"
        />
      )}
    </th>
  );
};

export function TransactionSummaryScreen() {
  const { user } = useAuth();
  const { showNotification } = useNotification();
  const navigate = useNavigate();

  // State variables for new workflow
  const [todayTransactions, setTodayTransactions] = useState<TransactionSummaryDetailItem[]>([]);
  const [pendingTransactions, setPendingTransactions] = useState<TransactionSummaryDetailItem[]>([]);
  const [merchantSummary, setMerchantSummary] = useState<MerchantSummary[]>([]);

  // Loading states
  const [todayLoading, setTodayLoading] = useState(false);
  const [pendingLoading, setPendingLoading] = useState(false);
  const [summaryLoading, setSummaryLoading] = useState(false);

  // Active tab state
  const [activeTab, setActiveTab] = useState<'today' | 'pending' | 'summary'>('summary');
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null);

  // Running Number section states
  const [runningDate, setRunningDate] = useState<string>(() => {
    const today = new Date();
    return today.toISOString().split('T')[0]; // Format: YYYY-MM-DD
  });
  const [trxNumber, setTrxNumber] = useState<string>('');
  const [loadingTrxNumber, setLoadingTrxNumber] = useState(false);

  // Invoice date states
  const [invBblDate, setInvBblDate] = useState<string>(() => {
    const today = new Date();
    return today.toISOString().split('T')[0]; // Format: YYYY-MM-DD
  });
  const [invOthDate, setInvOthDate] = useState<string>(() => {
    const today = new Date();
    return today.toISOString().split('T')[0]; // Format: YYYY-MM-DD
  });

  // Selection states for bulk operations
  const [selectedTodayIds, setSelectedTodayIds] = useState<Set<number>>(new Set());
  const [selectedPendingIds, setSelectedPendingIds] = useState<Set<number>>(new Set());
  const [bulkApproving, setBulkApproving] = useState(false);

  // Confirmation modal state
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [confirmationData, setConfirmationData] = useState<{
    count: number;
    tab: string;
    selectedIds: Set<number>;
  } | null>(null);

  // Transaction Log Search Modal state
  const [showSearchModal, setShowSearchModal] = useState(false);

  // Resizable columns state
  const [columnWidths, setColumnWidths] = useState<Record<string, number>>(() => {
    const initialWidths: Record<string, number> = {};
    tableColumns.forEach(col => {
      initialWidths[col.key] = col.defaultWidth;
    });
    return initialWidths;
  });

  // Refs for resizing functionality
  const tableRef = useRef<HTMLTableElement>(null);
  const resizingColumn = useRef<string | null>(null);
  const startX = useRef<number>(0);
  const startWidth = useRef<number>(0);

  // Mouse event handlers for column resizing
  const handleMouseDown = useCallback((e: React.MouseEvent, columnKey: string) => {
    console.log('🔥 STARTING RESIZE for column:', columnKey);
    e.preventDefault();
    e.stopPropagation();
    
    resizingColumn.current = columnKey;
    startX.current = e.clientX;
    startWidth.current = columnWidths[columnKey];
    
    console.log('📊 Initial resize values:', { 
      columnKey, 
      startX: startX.current, 
      startWidth: startWidth.current,
      clientX: e.clientX
    });
    
    // Add global event listeners
    const handleMouseMove = (moveEvent: MouseEvent) => {
      if (!resizingColumn.current) return;
      
      const deltaX = moveEvent.clientX - startX.current;
      const column = tableColumns.find(col => col.key === resizingColumn.current);
      const minWidth = column?.minWidth || 80;
      const newWidth = Math.max(minWidth, startWidth.current + deltaX);
      
      console.log('🔄 RESIZING:', { 
        column: resizingColumn.current, 
        deltaX, 
        newWidth, 
        minWidth,
        currentX: moveEvent.clientX,
        startX: startX.current,
        calculated: startWidth.current + deltaX
      });
      
      setColumnWidths(prev => ({
        ...prev,
        [resizingColumn.current!]: newWidth
      }));
    };
    
    const handleMouseUp = () => {
      console.log('✅ ENDING RESIZE for column:', resizingColumn.current);
      
      resizingColumn.current = null;
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
      document.body.classList.remove('resizing');
    };
    
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    document.body.style.cursor = 'col-resize';
    document.body.style.userSelect = 'none';
    document.body.classList.add('resizing');
  }, [columnWidths]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    // This is now handled inline above
  }, []);

  const handleMouseUp = useCallback(() => {
    // This is now handled inline above
  }, []);

  // Cleanup event listeners on unmount
  useEffect(() => {
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [handleMouseMove, handleMouseUp]);


  console.log("merchantSummary", merchantSummary);

  // Reset column widths to default
  const resetColumnWidths = useCallback(() => {
    const defaultWidths: Record<string, number> = {};
    tableColumns.forEach(col => {
      defaultWidths[col.key] = col.defaultWidth;
    });
    setColumnWidths(defaultWidths);
  }, []);

  // Helper function to render cell content based on column key
  const renderCellContent = (item: TransactionSummaryDetailItem, columnKey: string, isToday: boolean = true) => {
    const isNegative = item.total_amount < 0;
    // let baseClass = isNegative ? 'text-red-600 font-semibold' : 'text-gray-900';
    let baseClass = 'text-gray-900';
    // Highlight merchant name if trade_status is REFUND or ADJUST
    if (item.trade_status?.toUpperCase() === 'REFUND' || item.trade_status?.toUpperCase() === 'ADJUST') {
      baseClass = 'text-red-600 font-semibold';
    }
    
    switch (columnKey) {
      case 'select':
        // Don't show checkbox for already transferred transactions in Today's tab
        if (isToday && item.is_transfer === 1) {
          return (
            <span className="text-green-600 text-xs font-medium">
              ✓
            </span>
          );
        }
        return (
          <input
            type="checkbox"
            checked={isToday ? selectedTodayIds.has(item.id) : selectedPendingIds.has(item.id)}
            onChange={(e) => isToday ? handleSelectToday(item.id, e.target.checked) : handleSelectPending(item.id, e.target.checked)}
            className={`rounded ${isToday ? 'border-blue-300 text-blue-600 focus:ring-blue-500' : 'border-orange-300 text-orange-600 focus:ring-orange-500'}`}
          />
        );
      case 'date':
        return formatDateYearMonthDay(item.transaction_date);
      case 'merchant_vat':
        return <span className={baseClass}>{item.merchant_vat}</span>;
      case 'merchant_name':
        // If trade_status is 'REFUND' or 'ADJUST', append label to merchant name
        let tradeLabel = '';
        if (item.trade_status === 'REFUND') {
          tradeLabel = ' (Refund)';
        } else if (item.trade_status?.toUpperCase() === 'ADJUST') {
          tradeLabel = ' (Adjust)';
        }
        return (
          <span className={baseClass}>
        {item.merchant_name}
        {tradeLabel}
          </span>
        );
      case 'merchant_ref':
        return <span className={baseClass}>{item.merchant_ref || '-'}</span>;
      case 'bank_account_no':
        return <span className={baseClass}>{item.bank_account_no || '-'}</span>;
      case 'bank_name':
        const bankName = item.bank_name_en || item.bank_name_th;
        const bankCode = item.bank_code;
        if (bankCode && bankName) {
          return <span className={baseClass}>{bankCode} - {bankName}</span>;
        } else if (bankName) {
          return <span className={baseClass}>{bankName}</span>;
        } else {
          return <span className={baseClass}>-</span>;
        }
      case 'mdr_rate':
        return <span className={baseClass}>{formatPercentage(item.mdr_rate)}</span>;
      case 'transaction_count':
        return <span className={item.transaction_count < 0 ? 'text-red-600 font-semibold' : 'text-gray-900'}>{item.transaction_count}</span>;
      case 'total_amount':
        return <span className={baseClass}>{formatCurrency(item.total_amount)}</span>;
      case 'mdr_amount':
        return <span className={baseClass}>{formatCurrency(item.mdr_amount)}</span>;
      case 'transfer_fee':
        return <span className={baseClass}>{formatCurrency(item.transfer_fee)}</span>;
      case 'vat_amount':
        return <span className={baseClass}>{formatCurrency(item.vat_amount)}</span>;
      case 'net_amount':
        return <span className={baseClass}>{formatCurrency(item.net_amount)}</span>;
      case 'withhold_tax':
        return <span className={baseClass}>{formatCurrency(item.withhold_tax)}</span>;
      case 'reimbursement_fee':
        return <span className={baseClass}>{formatCurrency(item.reimbursement_fee)}</span>;
      case 'service_fee':
        return <span className={baseClass}>{formatCurrency(item.service_fee)}</span>;
      case 'cup_business_tax_fee':
        return <span className={baseClass}>{formatCurrency(item.cup_business_tax_fee)}</span>;
      case 'final_net_amount':
        return <span className={`font-semibold ${item.final_net_amount < 0 ? 'text-red-600' : isToday ? 'text-blue-800' : 'text-orange-800'}`}>{formatCurrency(item.final_net_amount)}</span>;
      case 'channel_type':
        return <span className={baseClass}>{item.channel_type}</span>;
      case 'transfer_status':
        return (
          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
            item.is_transfer === 1 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            {item.is_transfer === 1 ? 'Transferred' : 'Pending'}
          </span>
        );
      case 'actions':
        return (
          <RoleBasedComponent requiredPermission="canCreate">
            {item.is_transfer === 0 ? (
              <button
                onClick={() => updateTransferStatus(item.id, 1)}
                className="text-green-600 hover:text-green-900 bg-green-100 hover:bg-green-200 px-3 py-1 rounded text-xs font-medium"
              >
                Mark Transferred
              </button>
            ) : (
              <button
                onClick={() => updateTransferStatus(item.id, 0)}
                className="text-red-600 hover:text-red-900 bg-red-100 hover:bg-red-200 px-3 py-1 rounded text-xs font-medium"
              >
                Mark Pending
              </button>
            )}
          </RoleBasedComponent>
        );
      default:
        return null;
    }
  };

  // Load today's transactions (current date)
  const loadTodayTransactions = async () => {
    setTodayLoading(true);
    try {
      const today = new Date().toISOString().split('T')[0];
      console.log('🔍 Loading today\'s transactions for:', today);

      const result = await safeIpcInvoke('get-transaction-summary-today', {
        startDate: today,
        endDate: today,
        page: 1,
        pageSize: 1000
      });

      if (result.success) {
        setTodayTransactions(result.data || []);
        console.log(`📋 Loaded ${result.data?.length || 0} today's transactions`);
      } else {
        showNotification('Failed to load today\'s transactions: ' + (result.error || 'Unknown error'), 'error');
        setTodayTransactions([]);
      }
    } catch (error) {
      console.error('Error loading today\'s transactions:', error);
      showNotification('Error loading today\'s transactions', 'error');
      setTodayTransactions([]);
    } finally {
      setTodayLoading(false);
      setLastRefresh(new Date());
    }
  };

  // Load all pending transactions (across all dates)
  const loadPendingTransactions = async () => {
    setPendingLoading(true);
    try {
      console.log('🔍 Loading all pending transactions...');

      const result = await safeIpcInvoke('get-transaction-summary-details', {
        isTransfer: 0, // Only pending transactions
        page: 1,
        pageSize: 1000
      });

      if (result.success) {
        setPendingTransactions(result.data || []);
        console.log(`📋 Loaded ${result.data?.length || 0} pending transactions`);
      } else {
        showNotification('Failed to load pending transactions: ' + (result.error || 'Unknown error'), 'error');
        setPendingTransactions([]);
      }
    } catch (error) {
      console.error('Error loading pending transactions:', error);
      showNotification('Error loading pending transactions', 'error');
      setPendingTransactions([]);
    } finally {
      setPendingLoading(false);
      setLastRefresh(new Date());
    }
  };

  // Generate merchant summary from all transaction data
  const generateMerchantSummary = () => {
    setSummaryLoading(true);
    try {
      // Use only today's transactions that are transferred (is_transfer === 1)
      const transferredTodayTransactions = todayTransactions.filter(transaction => transaction.is_transfer === 1);
      
      console.log(`📊 Processing ${todayTransactions.length} today's transactions, ${transferredTodayTransactions.length} transferred`);
      
      // Group by merchant VAT
      const merchantGroups = transferredTodayTransactions.reduce((groups, transaction) => {
        const key = transaction.merchant_vat;
        if (!groups[key]) {
          groups[key] = {
            merchant_vat: transaction.merchant_vat,
            merchant_name: transaction.merchant_name,
            transactions: []
          };
        }
        groups[key].transactions.push(transaction);
        return groups;
      }, {} as Record<string, { merchant_vat: string; merchant_name: string; transactions: TransactionSummaryDetailItem[] }>);

      // Calculate summary for each merchant (only transferred transactions from today)

      console.log("merchantGroups",merchantGroups)
      const summary: MerchantSummary[] = Object.values(merchantGroups).map(group => {
        // All transactions are already transferred since we filtered above
        // Get bank information from the first transaction (assuming all transactions for a merchant have the same bank)
        const firstTransaction = group.transactions[0];
        return {
          merchant_vat: group.merchant_vat,
          merchant_name: group.merchant_name,
          total_transactions: group.transactions.length,
          total_amount: group.transactions.reduce((sum, t) => Number(sum) + Number(t.net_amount), 0),
          transferred_amount: group.transactions.reduce((sum, t) => Number(sum) + Number(t.net_amount), 0),
          pending_amount: 0, // No pending since we only show transferred
          transferred_count: group.transactions.length,
          pending_count: 0, // No pending since we only show transferred
          // Add bank information
          bank_id: firstTransaction?.bank_id,
          bank_account_no: firstTransaction?.bank_account_no,
          bank_code: firstTransaction?.bank_code,
          bank_name_th: firstTransaction?.bank_name_th,
          bank_name_en: firstTransaction?.bank_name_en,
          merchant_ref: firstTransaction?.merchant_ref || '',
          trx_no: firstTransaction?.trx_no || ''
        };
      });

      // // Sort by trx_no ascending (if trx_no is present, otherwise fallback to merchant_vat)
      // summary.sort((a, b) => {
      //   if (a.trx_no && b.trx_no) {
      //     return a.trx_no.localeCompare(b.trx_no, undefined, { numeric: true });
      //   }
      //   return a.merchant_vat.localeCompare(b.merchant_vat);
      // });
      
      setMerchantSummary(summary);
      console.log(`📊 Generated merchant summary for ${summary.length} merchants with transferred transactions`);
      console.log('📊 Summary statistics:', {
        totalMerchants: summary.length,
        totalAmount: summary.reduce((sum, item) => sum + item.total_amount, 0),
        avgPerMerchant: summary.length > 0 ? summary.reduce((sum, item) => sum + item.total_amount, 0) / summary.length : 0,
        totalTransactions: summary.reduce((sum, item) => sum + item.total_transactions, 0),
        totalTransferred: summary.reduce((sum, item) => sum + item.transferred_count, 0),
        completionRate: 100 // Always 100% since we only show transferred
      });
    } catch (error) {
      console.error('Error generating merchant summary:', error);
      showNotification('Error generating merchant summary', 'error');
      setMerchantSummary([]);
    } finally {
      setSummaryLoading(false);
    }
  };

  // Function to get the next transaction number from transaction_payment_form table
  const getNextTrxNumber = async () => {
    setLoadingTrxNumber(true);
    try {
      console.log('🔢 Getting next transaction number...');
      
      const result = await safeIpcInvoke('get-next-trx-number');
      
      if (result.success && result.data) {
        setTrxNumber(result.data.next_trx_no || '');
        console.log(`📝 Next transaction number: ${result.data.next_trx_no}`);
      } else {
        console.warn('No transaction number returned from database');
        setTrxNumber('');
      }
    } catch (error) {
      console.error('Error getting next transaction number:', error);
      showNotification('Error loading transaction number', 'error');
      setTrxNumber('');
    } finally {
      setLoadingTrxNumber(false);
    }
  };

  // Event handlers for new workflow
  const handleRefreshAll = async () => {
    try {
      // Clear selections when refreshing
      setSelectedTodayIds(new Set());
      setSelectedPendingIds(new Set());

      await Promise.all([
        loadTodayTransactions(),
        loadPendingTransactions()
      ]);
      
      // Merchant summary will be auto-generated from useEffect when todayTransactions changes
      
      showNotification('All transaction data refreshed', 'success');
    } catch (error) {
      console.error('Error refreshing all data:', error);
      showNotification('Error refreshing data', 'error');
    }
  };

  // Selection helper functions
  const handleSelectToday = (id: number, checked: boolean) => {
    // Find the transaction to check if it's already transferred
    const transaction = todayTransactions.find(item => item.id === id);
    if (transaction && transaction.is_transfer === 1) {
      // Don't allow selection of already transferred transactions
      return;
    }
    
    const newSelected = new Set(selectedTodayIds);
    if (checked) {
      newSelected.add(id);
    } else {
      newSelected.delete(id);
    }
    setSelectedTodayIds(newSelected);
  };

  const handleSelectAllToday = (checked: boolean) => {
    if (checked) {
      // Only select transactions that are not already transferred (is_transfer !== 1)
      const selectableTransactions = todayTransactions.filter(item => item.is_transfer !== 1);
      const allIds = new Set(selectableTransactions.map(item => item.id));
      setSelectedTodayIds(allIds);
    } else {
      setSelectedTodayIds(new Set());
    }
  };

  const handleSelectPending = (id: number, checked: boolean) => {
    const newSelected = new Set(selectedPendingIds);
    if (checked) {
      newSelected.add(id);
    } else {
      newSelected.delete(id);
    }
    setSelectedPendingIds(newSelected);
  };

  const handleSelectAllPending = (checked: boolean) => {
    if (checked) {
      const allIds = new Set(pendingTransactions.map(item => item.id));
      setSelectedPendingIds(allIds);
    } else {
      setSelectedPendingIds(new Set());
    }
  };

  const updateTransferStatus = async (detailId: number, isTransfer: number) => {
    if (!user?.user_name) {
      showNotification('User not authenticated', 'error');
      return;
    }

    try {
      const result = await safeIpcInvoke('update-transfer-status', detailId, isTransfer, user.user_name);

      if (result.success) {
        showNotification(result.message, 'success');
        // Refresh all data to reflect changes
        await handleRefreshAll();
      } else {
        showNotification('Failed to update transfer status: ' + (result.error || 'Unknown error'), 'error');
      }
    } catch (error) {
      console.error('Error updating transfer status:', error);
      showNotification('Error updating transfer status', 'error');
    }
  };

  // Bulk approval function with confirmation
  const handleBulkApprove = async () => {
    if (!user?.user_name) {
      showNotification('User not authenticated', 'error');
      return;
    }

    const selectedIds = activeTab === 'today' ? selectedTodayIds : selectedPendingIds;

    if (selectedIds.size === 0) {
      showNotification('Please select transactions to approve', 'warning');
      return;
    }

    // Show confirmation modal
    setConfirmationData({
      count: selectedIds.size,
      tab: activeTab,
      selectedIds: new Set(selectedIds)
    });
    setShowConfirmModal(true);
  };

  // Execute bulk approval after confirmation - Enhanced workflow with running number integration
  const executeBulkApproval = async () => {
    if (!confirmationData || !user?.user_name) {
      return;
    }

    setBulkApproving(true);
    setShowConfirmModal(false);

    try {
      console.log('🚀 Starting enhanced approval workflow...');
      
      // Use the new enhanced workflow that integrates running number system
      const result = await safeIpcInvoke('approve-selected-transactions', {
        detailIds: Array.from(confirmationData.selectedIds),
        updatedBy: user.user_name,
        runningDate: runningDate, // Use the current running date from the form
        transferDtBbl: invBblDate || null, // Include BBL invoice date
        transferDtOth: invOthDate || null  // Include Other invoice date
      });

      if (result.success) {
        const data = result.data;
        
        // Show comprehensive success notification
        showNotification(
          `✅ Approved ${data.approved_count} transaction(s) with Transaction Number: ${data.trx_no}. Total Amount: ${formatCurrency(data.total_amount)}`,
          'success'
        );

        console.log('🎉 Enhanced approval completed:', {
          trx_no: data.trx_no,
          payment_form_id: data.payment_form_id,
          approved_count: data.approved_count,
          total_amount: data.total_amount,
          net_amount: data.net_amount
        });

        // Update the transaction number display to show the next available number
        await getNextTrxNumber();

        // Clear selections and refresh data
        if (confirmationData.tab === 'today') {
          setSelectedTodayIds(new Set());
        } else {
          setSelectedPendingIds(new Set());
        }

        await handleRefreshAll();
        
        // Show additional details in console for tracking
        console.log('📋 Payment Form Created:', {
          'Transaction Number': data.trx_no,
          'Payment Form ID': data.payment_form_id,
          'Approved Transactions': data.approved_count,
          'Total Amount': data.total_amount,
          'Net Amount': data.net_amount,
          'VAT Amount': data.vat_amount,
          'Withholding Tax': data.withhold_amount,
          'MDR Amount': data.mdr_amount,
          'Created By': data.created_by,
          'Created At': data.created_at
        });

      } else {
        showNotification(`Failed to approve transactions: ${result.error}`, 'error');
        console.error('❌ Enhanced approval failed:', result.error);
      }
    } catch (error) {
      console.error('❌ Error in enhanced approval workflow:', error);
      showNotification('Error during enhanced approval workflow', 'error');
    } finally {
      setBulkApproving(false);
      setConfirmationData(null);
    }
  };

  // Cancel bulk approval
  const cancelBulkApproval = () => {
    setShowConfirmModal(false);
    setConfirmationData(null);
  };

  const handleExport = () => {
    let dataToExport: any[] = [];
    let filename = '';

    switch (activeTab) {
      case 'today':
        dataToExport = todayTransactions.map(item => ({
          'Transaction Date': item.transaction_date,
          'Merchant VAT': item.merchant_vat,
          'Merchant Name': item.merchant_name,
          'Merchant Ref': item.merchant_ref || '-',
          'Account Bank No': item.bank_account_no || '-',
          'Bank Name': item.bank_code && (item.bank_name_en || item.bank_name_th)
            ? `${item.bank_code} - ${item.bank_name_en || item.bank_name_th}`
            : item.bank_name_en || item.bank_name_th || '-',
          'Channel Type': item.channel_type,
          'Transaction Count': item.transaction_count,
          'Total Amount': item.total_amount.toFixed(2),
          'MDR Amount': item.mdr_amount.toFixed(2),
          'VAT Amount': item.vat_amount.toFixed(2),
          'Final Net Amount': item.final_net_amount.toFixed(2),
          'Transfer Status': item.is_transfer === 1 ? 'Transferred' : 'Pending'
        }));
        filename = `transactions-today-${new Date().toISOString().split('T')[0]}.csv`;
        break;
      case 'pending':
        dataToExport = pendingTransactions.map(item => ({
          'Transaction Date': item.transaction_date,
          'Merchant VAT': item.merchant_vat,
          'Merchant Name': item.merchant_name,
          'Merchant Ref': item.merchant_ref || '-',
          'Account Bank No': item.bank_account_no || '-',
          'Bank Name': item.bank_code && (item.bank_name_en || item.bank_name_th)
            ? `${item.bank_code} - ${item.bank_name_en || item.bank_name_th}`
            : item.bank_name_en || item.bank_name_th || '-',
          'Channel Type': item.channel_type,
          'Transaction Count': item.transaction_count,
          'Total Amount': item.total_amount.toFixed(2),
          'MDR Amount': item.mdr_amount.toFixed(2),
          'VAT Amount': item.vat_amount.toFixed(2),
          'Final Net Amount': item.final_net_amount.toFixed(2),
          'Transfer Status': item.is_transfer === 1 ? 'Transferred' : 'Pending'
        }));
        filename = `transactions-pending-${new Date().toISOString().split('T')[0]}.csv`;
        break;
      case 'summary':
        dataToExport = merchantSummary.map(item => ({
          'Merchant VAT': item.merchant_vat,
          'Merchant Name': item.merchant_name,
          'Merchant Ref': item.merchant_ref || '-',
          'Account Bank No': item.bank_account_no || '-',
          'Bank Name': item.bank_code && (item.bank_name_en || item.bank_name_th)
            ? `${item.bank_code} - ${item.bank_name_en || item.bank_name_th}`
            : item.bank_name_en || item.bank_name_th || '-',
          'Total Transactions': item.total_transactions,
          'Total Amount': item.total_amount.toFixed(2),
          'Transferred Count': item.transferred_count,
          'Transferred Amount': item.transferred_amount.toFixed(2),
          'Pending Count': item.pending_count,
          'Pending Amount': item.pending_amount.toFixed(2)
        }));
        filename = `merchant-summary-${new Date().toISOString().split('T')[0]}.csv`;
        break;
      default:
        dataToExport = [];
    }

    if (dataToExport.length === 0) {
      showNotification('No data to export', 'warning');
      return;
    }

    const csvContent = [
      Object.keys(dataToExport[0]).join(','),
      ...dataToExport.map(row => Object.values(row).join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.click();
    URL.revokeObjectURL(url);

    showNotification('Data exported successfully', 'success');
  };

  const handlePrint = () => {
    window.print();
  };

  // Format currency safely
  const formatCurrency = (amount: number | string | null | undefined) => {
    const numAmount = Number(amount);
    if (amount === null || amount === undefined || isNaN(numAmount)) {
      return new Intl.NumberFormat('th-TH', {
        style: 'currency',
        currency: 'THB',
        minimumFractionDigits: 2
      }).format(0);
    }
    return new Intl.NumberFormat('th-TH', {
      style: 'currency',
      currency: 'THB',
      minimumFractionDigits: 2
    }).format(numAmount);
  };

  // Format date safely for display
  const formatDateTime = (date: Date | string | null | undefined) => {
    if (!date) return '';
    try {
      if (date instanceof Date) {
        return date.toLocaleTimeString();
      }
      if (typeof date === 'string') {
        return new Date(date).toLocaleTimeString();
      }
      return String(date);
    } catch (error) {
      console.error('Error formatting date:', error);
      return String(date);
    }
  };

  // Format date for table display (date only)
  const formatDate = (date: Date | string | null | undefined) => {
    if (!date) return '';
    try {
      if (date instanceof Date) {
        return date.toLocaleDateString();
      }
      if (typeof date === 'string') {
        // If it's already a string in YYYY-MM-DD format, return as is
        if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
          return date;
        }
        return new Date(date).toLocaleDateString();
      }
      return String(date);
    } catch (error) {
      console.error('Error formatting date:', error);
      return String(date);
    }
  };

  // Safely format percentage
  const formatPercentage = (value: number | null | undefined) => {
    if (value === null || value === undefined || isNaN(Number(value))) {
      return '0.00%';
    }
    return `${Number(value).toFixed(2)}%`;
  };

  // Load initial data for all tabs
  useEffect(() => {
    const initializeData = async () => {
      console.log('🚀 Initializing transaction summary data...');

      try {
        // Load all data concurrently for the new workflow
        await Promise.all([
          loadTodayTransactions(),
          loadPendingTransactions()
        ]);

        console.log('✅ Initial data loading complete');
      } catch (error) {
        console.error('❌ Error during initialization:', error);
        showNotification('Error loading initial data', 'error');
      }
    };

    initializeData();
  }, []); // Empty dependency array for one-time initialization

  // Generate merchant summary when transaction data changes
  useEffect(() => {
    if (todayTransactions.length > 0) {
      generateMerchantSummary();
    }
  }, [todayTransactions]);

  // Handle tab changes to ensure data is fresh
  useEffect(() => {
    if (activeTab === 'today' && todayTransactions.length === 0 && !todayLoading) {
      console.log('📋 Switching to today tab - loading data if empty');
      loadTodayTransactions();
    }
    if (activeTab === 'pending' && pendingTransactions.length === 0 && !pendingLoading) {
      console.log('📋 Switching to pending tab - loading data if empty');
      loadPendingTransactions();
    }
    if (activeTab === 'summary' && merchantSummary.length === 0 && !summaryLoading) {
      console.log('📊 Switching to summary tab - generating merchant summary');
      generateMerchantSummary();
    }
  }, [activeTab]);

  // Load initial data and transaction number when component mounts
  useEffect(() => {
    getNextTrxNumber();
  }, []);

  // Reload transaction number when running date changes
  useEffect(() => {
    if (runningDate) {
      getNextTrxNumber();
    }
  }, [runningDate]);

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* Add global styles for resizing */}
      <style>{`
        /* Global resize styles */
        .resizing {
          cursor: col-resize !important;
          user-select: none !important;
        }
        .resizing * {
          cursor: col-resize !important;
          user-select: none !important;
        }
        
        /* Table layout styles */
        .resizable-table {
          table-layout: fixed;
          border-collapse: separate;
          border-spacing: 0;
          width: 100%;
        }
        
        .resizable-table th {
          border-right: 1px solid #e5e7eb;
          position: relative;
          overflow: hidden;
        }
        
        .resizable-table td {
          border-right: 1px solid #e5e7eb;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        
        /* Resize handle styles */
        .resize-handle {
          position: absolute;
          top: 0;
          right: 0;
          width: 4px;
          height: 100%;
          cursor: col-resize;
          z-index: 10;
          background: rgba(59, 130, 246, 0.3);
          border-right: 1px solid rgba(0, 0, 0, 0.1);
          transition: background-color 0.2s ease;
        }
        
        .resize-handle:hover {
          background: rgba(59, 130, 246, 0.6) !important;
        }
        
        .resize-handle.orange {
          background: rgba(249, 115, 22, 0.3);
        }
        
        .resize-handle.orange:hover {
          background: rgba(249, 115, 22, 0.6) !important;
        }
        
        /* Debug styles */
        .debug-resize {
          outline: 2px solid red;
        }
      `}</style>
      
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="bg-blue-600 text-white p-4 rounded-t-lg">
          <h1 className="text-xl font-bold">Operations Daily</h1>
        </div>

        {/* Running Number Section */}
        <div className="bg-white p-4 border-x border-t border-gray-300">
          <div className="bg-gray-50 border border-gray-300 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-3 flex items-center gap-2">
              Running Number
            </h3>
            <div className="flex flex-wrap gap-6 items-center">
              {/* Date Input */}
              <div className="flex items-center gap-3">
                <label htmlFor="running-date" className="text-sm font-medium text-gray-700 min-w-12">
                  Date
                </label>
                <input
                  id="running-date"
                  type="date"
                  value={runningDate}
                  onChange={(e) => setRunningDate(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm min-w-36"
                />
              </div>

              {/* Transaction Number Display */}
              <div className="flex items-center gap-3">
                <label className="text-sm font-medium text-gray-700 min-w-16">
                  Trx No.
                </label>
                <div className="flex items-center gap-2">
                  <div className="px-4 py-2 bg-orange-100 border border-orange-300 rounded-md text-sm font-medium text-gray-800 min-w-32 h-10 flex items-center justify-center">
                    {loadingTrxNumber ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-2 border-orange-600 border-t-transparent"></div>
                    ) : (
                      trxNumber || '---'
                    )}
                  </div>
                </div>
              </div>

              {/* Invoice BBL Date */}
              <div className="flex items-center gap-3">
                <label htmlFor="inv-bbl-date" className="text-sm font-medium text-gray-700 min-w-20">
                  Inv (BBL) Date
                </label>
                <input
                  id="inv-bbl-date"
                  type="date"
                  value={invBblDate}
                  onChange={(e) => setInvBblDate(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm min-w-36"
                />
              </div>

              {/* Invoice Other Date */}
              <div className="flex items-center gap-3">
                <label htmlFor="inv-oth-date" className="text-sm font-medium text-gray-700 min-w-20">
                  Inv (Oth) Date
                </label>
                <input
                  id="inv-oth-date"
                  type="date"
                  value={invOthDate}
                  onChange={(e) => setInvOthDate(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm min-w-36"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Controls */}
        <div className="bg-white p-4 border-x border-gray-300">
          <div className="flex flex-wrap gap-4 items-center">
            {/* Navigate to Filter Screen Button */}
            <Button
              onClick={() => navigate('/transaction-summary-filter')}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              Search Log Transactions
            </Button>

            {/* Search Log Transaction Button */}
            <Button
              onClick={() => setShowSearchModal(true)}
              className="bg-purple-600 hover:bg-purple-700 text-white"
            >
              Search Payment Forms
            </Button>

            {/* Reset Column Widths Button */}
            {/* <Button
              onClick={resetColumnWidths}
              className="bg-gray-600 hover:bg-gray-700 text-white"
              title="Reset all column widths to default"
            >
              📏 Reset Columns
            </Button> */}

            {/* Debug Button for Testing */}
            {/* <Button
              onClick={() => {
                console.log('🔍 DEBUG INFO:');
                console.log('Column widths:', columnWidths);
                console.log('Resize handles found:', document.querySelectorAll('.resize-handle').length);
                console.log('Table ref:', tableRef.current);
                
                // Make handles more visible for testing
                document.querySelectorAll('.resize-handle').forEach((handle, i) => {
                  console.log(`Handle ${i}:`, handle.getBoundingClientRect());
                  (handle as HTMLElement).style.background = 'red';
                  (handle as HTMLElement).style.opacity = '0.8';
                });
              }}
              className="bg-purple-600 hover:bg-purple-700 text-white"
              title="Debug resize functionality"
            >
              🐛 Debug Resize
            </Button> */}

            {/* Bulk Approve Button - only show for Today and Pending tabs */}
            {(activeTab === 'today' || activeTab === 'pending') && (
              <RoleBasedComponent requiredPermission="canCreate">
                <Button
                  onClick={handleBulkApprove}
                  disabled={bulkApproving || (activeTab === 'today' ? selectedTodayIds.size === 0 : selectedPendingIds.size === 0)}
                  className="bg-orange-600 hover:bg-orange-700 text-white"
                >
                  {bulkApproving ? 'Approving...' : `Approve Selected (${activeTab === 'today' ? selectedTodayIds.size : selectedPendingIds.size})`}
                </Button>
              </RoleBasedComponent>
            )}
          </div>
        </div>

        {/* Summary Section */}
        <div className="bg-white p-4 border-x border-gray-300">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">

            {/* Merchant Summary */}
            <div className="bg-purple-100 p-3 rounded">
              <div className="text-sm text-purple-700 flex items-center gap-2">
                Today's Transferred Summary
                {summaryLoading && (
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-purple-600 border-t-transparent"></div>
                )}
              </div>
              <div className="text-lg font-bold text-purple-800">
                {merchantSummary.length} merchants
              </div>
              <div className="text-sm text-purple-600">
                Total: {formatCurrency(merchantSummary.reduce((sum, item) => Number(sum) + Number(item.total_amount), 0))}
              </div>
            </div>

            {/* Today's Transactions */}
            <div className="bg-blue-100 p-3 rounded">
              <div className="text-sm text-blue-700 flex items-center gap-2">
                Today's Transactions
                {todayLoading && (
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-600 border-t-transparent"></div>
                )}
              </div>
              <div className="text-lg font-bold text-blue-800">
                {todayTransactions.length} records
              </div>
              <div className="text-sm text-blue-600">
                Total: {formatCurrency(todayTransactions.reduce((sum, item) => Number(sum) + Number(item.net_amount), 0))}
              </div>
            </div>

            {/* Pending Transactions */}
            <div className="bg-orange-100 p-3 rounded">
              <div className="text-sm text-orange-700 flex items-center gap-2">
                Pending Transactions
                {pendingLoading && (
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-orange-600 border-t-transparent"></div>
                )}
              </div>
              <div className="text-lg font-bold text-orange-800">
                {pendingTransactions.length} records
              </div>
              <div className="text-sm text-orange-600">
                Total: {formatCurrency(pendingTransactions.reduce((sum, item) => Number(sum) + Number(item.net_amount), 0))}
              </div>
            </div>

            

          </div>
        </div>

        {/* Tab Navigation */}
        <div className="bg-white border-x border-gray-300">
          <div className="flex border-b overflow-x-auto">
            <button
              onClick={() => setActiveTab('summary')}
              className={`px-4 py-2 font-medium whitespace-nowrap ${
                activeTab === 'summary'
                  ? 'border-b-2 border-purple-500 text-purple-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <span className="flex items-center gap-2">
                Transfer Today
                <span className="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full">
                  {merchantSummary.length}
                </span>
              </span>
            </button>
            <button
              onClick={() => setActiveTab('today')}
              className={`px-4 py-2 font-medium whitespace-nowrap ${
                activeTab === 'today'
                  ? 'border-b-2 border-blue-500 text-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <span className="flex items-center gap-2">
                List Transaction Today
                <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                  {todayTransactions.length}
                </span>
              </span>
            </button>
            <button
              onClick={() => setActiveTab('pending')}
              className={`px-4 py-2 font-medium whitespace-nowrap ${
                activeTab === 'pending'
                  ? 'border-b-2 border-orange-500 text-orange-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <span className="flex items-center gap-2">
                List Transaction Pending
                <span className="bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full">
                  {pendingTransactions.length}
                </span>
              </span>
            </button>
          </div>
        </div>

        {/* Content Area */}
        <div className="bg-white border border-gray-300 rounded-b-lg overflow-hidden">
          {/* Merchant Summary Tab */}
          {activeTab === 'summary' && (
            <div className="p-4">
              <div className="mb-4 bg-purple-50 border border-purple-200 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                  <h3 className="text-lg font-semibold text-purple-800">
                    Today's Transferred Net Amount
                  </h3>
                  {summaryLoading && (
                    <div className="animate-spin rounded-full h-5 w-5 border-2 border-purple-600 border-t-transparent"></div>
                  )}
                </div>
                <p className="text-sm text-purple-700">
                  Transferred transactions today: <strong>{merchantSummary.length}</strong>
                  {todayTransactions.length > 0 && (
                    <span className="ml-2">
                    Merchants
                    </span>
                  )}
                </p>
              </div>

              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 border border-gray-300">
                  <thead className="bg-purple-50">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-purple-700 uppercase tracking-wider border-r border-gray-300">
                        Trx. NO
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-purple-700 uppercase tracking-wider border-r border-gray-300">
                        Merchant VAT
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-purple-700 uppercase tracking-wider border-r border-gray-300">
                        Merchant Name
                      </th>
                      <th className="px-4 py-3 text-center text-xs font-medium text-purple-700 uppercase tracking-wider border-r border-gray-300">
                        MID
                      </th>
                      <th className="px-4 py-3 text-center text-xs font-medium text-purple-700 uppercase tracking-wider border-r border-gray-300">
                        Account No
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-purple-700 uppercase tracking-wider border-r border-gray-300">
                        Bank Name
                      </th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-purple-700 uppercase tracking-wider border-r border-gray-300">
                        Net Amount
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {summaryLoading && merchantSummary.length === 0 && (
                      [...Array(5)].map((_, index) => (
                        <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-purple-50'}>
                          {[...Array(6)].map((_, colIndex) => (
                            <td key={colIndex} className="px-4 py-4 whitespace-nowrap border-r border-gray-200">
                              <div className="animate-pulse bg-purple-200 h-4 rounded"></div>
                            </td>
                          ))}
                        </tr>
                      ))
                    )}
                    {merchantSummary.map((merchant, index) => (
                      <tr key={merchant.merchant_vat} className={index % 2 === 0 ? 'bg-white' : 'bg-purple-50'}>
                        <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900 border-r border-gray-200">
                          {merchant.trx_no}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900 border-r border-gray-200">
                          {merchant.merchant_vat}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 border-r border-gray-200">
                          <div className="max-w-xs truncate" title={merchant.merchant_name}>
                            {merchant.merchant_name}
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-center text-gray-900 border-r border-gray-200">
                          <span className="text-gray-600">
                            {merchant.merchant_ref || '-'}
                          </span>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-center text-gray-900 border-r border-gray-200">
                          <span className="text-gray-600">
                            {merchant.bank_account_no || '-'}
                          </span>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 border-r border-gray-200">
                          <div className="max-w-xs truncate" title={
                            merchant.bank_code && (merchant.bank_name_en || merchant.bank_name_th)
                              ? `${merchant.bank_code} - ${merchant.bank_name_en || merchant.bank_name_th}`
                              : merchant.bank_name_en || merchant.bank_name_th || '-'
                          }>
                            {merchant.bank_code && (merchant.bank_name_en || merchant.bank_name_th)
                              ? `${merchant.bank_code} - ${merchant.bank_name_en || merchant.bank_name_th}`
                              : merchant.bank_name_en || merchant.bank_name_th || '-'}
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-right text-gray-900 border-r border-gray-200">
                          <span className="font-semibold text-purple-800">
                            {formatCurrency(merchant.total_amount)}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                {merchantSummary.length === 0 && !summaryLoading && (
                  <div className="text-center py-8 text-gray-500">
                    <div className="text-purple-600 text-lg mb-2">📊</div>
                    <div>No transferred merchant data available for today</div>
                    <p className="text-sm mt-2">
                      {todayTransactions.length === 0 
                        ? "Load today's transaction data first" 
                        : todayTransactions.filter(t => t.is_transfer === 1).length === 0
                        ? "No transferred transactions found for today"
                        : "Processing merchant summary..."}
                    </p>
                  </div>
                )}
              </div>

              {/* Summary for merchant data */}
              {merchantSummary.length > 0 && (
                <div className="mt-4 bg-purple-50 border border-purple-200 rounded-lg p-4">
                  <h4 className="text-sm font-semibold text-purple-800 mb-2">Today's Transferred Merchant Summary Overview</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="text-purple-700">Merchants with Transfers:</span>
                      <span className="font-semibold text-purple-900 ml-2">{merchantSummary.length}</span>
                    </div>
                    <div>
                      <span className="text-purple-700">Total Transferred Amount:</span>
                      <span className="font-semibold text-purple-900 ml-2">
                        {formatCurrency(merchantSummary.reduce((sum, item) => sum + item.total_amount, 0))}
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
          {/* Today's Transactions Tab */}
          {activeTab === 'today' && (
            <div className="p-4">
              <div className="mb-4 bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <h3 className="text-lg font-semibold text-blue-800">
                    Today's Transactions ({formatDateYearMonthDay(new Date().toLocaleDateString())})
                  </h3>
                  {todayLoading && (
                    <div className="animate-spin rounded-full h-5 w-5 border-2 border-blue-600 border-t-transparent"></div>
                  )}
                </div>
                <p className="text-sm text-blue-700">
                  Total records: <strong>{todayTransactions.length}</strong>
                  {todayTransactions.length > 0 && (
                    <span className="ml-2">
                      (Selectable: <strong>{todayTransactions.filter(item => item.is_transfer !== 1).length}</strong>)
                    </span>
                  )}
                </p>
                
              </div>

              <div className="overflow-x-auto">
                <table ref={tableRef} className="resizable-table min-w-full divide-y divide-gray-200">
                  <thead className="bg-blue-50">
                    <tr>
                      {tableColumns.map((column) => (
                        <ResizableHeader
                          key={column.key}
                          column={column}
                          width={columnWidths[column.key]}
                          onMouseDown={handleMouseDown}
                          isLoading={todayLoading}
                          className="text-blue-700"
                        >
                          {column.key === 'select' && !todayLoading ? (
                            <input
                              type="checkbox"
                              checked={(() => {
                                const selectableTransactions = todayTransactions.filter(item => item.is_transfer !== 1);
                                return selectableTransactions.length > 0 && selectedTodayIds.size === selectableTransactions.length;
                              })()}
                              onChange={(e) => handleSelectAllToday(e.target.checked)}
                              className="rounded border-blue-300 text-blue-600 focus:ring-blue-500"
                            />
                          ) : column.label ? (
                            <div className="min-w-0 truncate">{column.label}</div>
                          ) : null}
                        </ResizableHeader>
                      ))}
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {todayLoading && todayTransactions.length === 0 && (
                      [...Array(5)].map((_, index) => (
                        <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-blue-50'}>
                          {tableColumns.map((column) => (
                            <td 
                              key={column.key} 
                              className="px-2 py-4"
                              style={{ 
                                width: `${columnWidths[column.key]}px`,
                                maxWidth: `${columnWidths[column.key]}px`,
                                minWidth: `${column.minWidth}px`
                              }}
                            >
                              <div 
                                className="animate-pulse bg-blue-200 h-4 rounded mx-2"
                                style={{ maxWidth: `${columnWidths[column.key] - 16}px` }}
                              ></div>
                            </td>
                          ))}
                        </tr>
                      ))
                    )}
                    {todayTransactions.map((item, index) => (
                      <tr key={item.id} className={index % 2 === 0 ? 'bg-white' : 'bg-blue-50'}>
                        {tableColumns.map((column) => (
                          <td 
                            key={column.key}
                            className="px-2 py-4 text-sm text-center"
                            style={{ 
                              width: `${columnWidths[column.key]}px`,
                              maxWidth: `${columnWidths[column.key]}px`,
                              minWidth: `${column.minWidth}px`
                            }}
                          >
                            <div className="truncate" style={{ maxWidth: `${columnWidths[column.key] - 16}px` }}>
                              {renderCellContent(item, column.key, true)}
                            </div>
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
                {todayTransactions.length === 0 && !todayLoading && (
                  <div className="text-center py-8 text-gray-500">
                    <div className="text-blue-600 text-lg mb-2">📅</div>
                    <div>No transactions found for today</div>
                  </div>
                )}
              </div>

              {/* Summary for today's transactions */}
              {todayTransactions.length > 0 && (
                <div className="mt-4 bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="text-sm font-semibold text-blue-800 mb-2">Today's Summary</h4>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-blue-700">Total Records:</span>
                      <span className="font-semibold text-blue-900 ml-2">{todayTransactions.length}</span>
                    </div>
                    <div>
                      <span className="text-blue-700">Total Amount:</span>
                      <span className="font-semibold text-blue-900 ml-2">
                        {formatCurrency(todayTransactions.reduce((sum, item) => Number(sum) + Number(item.net_amount), 0))}
                      </span>
                    </div>
                    <div>
                      <span className="text-blue-700">Transferred:</span>
                      <span className="font-semibold text-blue-900 ml-2">
                        {todayTransactions.filter(item => item.is_transfer === 1).length}
                      </span>
                    </div>
                    <div>
                      <span className="text-blue-700">Pending:</span>
                      <span className="font-semibold text-blue-900 ml-2">
                        {todayTransactions.filter(item => item.is_transfer === 0).length}
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
          {/* Pending Transactions Tab */}
          {activeTab === 'pending' && (
            <div className="p-4">
              <div className="mb-4 bg-orange-50 border border-orange-200 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <div className="w-3 h-3 bg-orange-500 rounded-full animate-pulse"></div>
                  <h3 className="text-lg font-semibold text-orange-800">
                    All Pending Transactions
                  </h3>
                  {pendingLoading && (
                    <div className="animate-spin rounded-full h-5 w-5 border-2 border-orange-600 border-t-transparent"></div>
                  )}
                </div>
                <p className="text-sm text-orange-700">
                  Total records: <strong>{pendingTransactions.length}</strong>
                </p>
                
              </div>

              <div className="overflow-x-auto">
                <table className="resizable-table min-w-full divide-y divide-gray-200">
                  <thead className="bg-orange-50">
                    <tr>
                      {tableColumns.map((column) => (
                        <ResizableHeader
                          key={column.key}
                          column={column}
                          width={columnWidths[column.key]}
                          onMouseDown={handleMouseDown}
                          isLoading={pendingLoading}
                          className="text-orange-700"
                        >
                          {column.key === 'select' && !pendingLoading ? (
                            <input
                              type="checkbox"
                              checked={pendingTransactions.length > 0 && selectedPendingIds.size === pendingTransactions.length}
                              onChange={(e) => handleSelectAllPending(e.target.checked)}
                              className="rounded border-orange-300 text-orange-600 focus:ring-orange-500"
                            />
                          ) : column.label ? (
                            <div className="min-w-0 truncate">{column.label}</div>
                          ) : null}
                        </ResizableHeader>
                      ))}
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {pendingLoading && pendingTransactions.length === 0 && (
                      [...Array(5)].map((_, index) => (
                        <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-orange-50'}>
                          {tableColumns.map((column) => (
                            <td 
                              key={column.key} 
                              className="px-2 py-4"
                              style={{ 
                                width: `${columnWidths[column.key]}px`,
                                maxWidth: `${columnWidths[column.key]}px`,
                                minWidth: `${column.minWidth}px`
                              }}
                            >
                              <div 
                                className="animate-pulse bg-orange-200 h-4 rounded mx-2"
                                style={{ maxWidth: `${columnWidths[column.key] - 16}px` }}
                              ></div>
                            </td>
                          ))}
                        </tr>
                      ))
                    )}
                    {pendingTransactions.map((item, index) => (
                      <tr key={item.id} className={index % 2 === 0 ? 'bg-white' : 'bg-orange-50'}>
                        {tableColumns.map((column) => (
                          <td 
                            key={column.key}
                            className="px-2 py-4 text-sm text-center"
                            style={{ 
                              width: `${columnWidths[column.key]}px`,
                              maxWidth: `${columnWidths[column.key]}px`,
                              minWidth: `${column.minWidth}px`
                            }}
                          >
                            <div className="truncate" style={{ maxWidth: `${columnWidths[column.key] - 16}px` }}>
                              {renderCellContent(item, column.key, false)}
                            </div>
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
                {pendingTransactions.length === 0 && !pendingLoading && (
                  <div className="text-center py-8 text-gray-500">
                    <div className="text-green-600 text-lg mb-2">🎉</div>
                    <div>No pending transactions found - All transfers are complete!</div>
                  </div>
                )}
              </div>

              {/* Summary for pending transactions */}
              {pendingTransactions.length > 0 && (
                <div className="mt-4 bg-orange-50 border border-orange-200 rounded-lg p-4">
                  <h4 className="text-sm font-semibold text-orange-800 mb-2">Pending Transactions Summary</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="text-orange-700">Total Records:</span>
                      <span className="font-semibold text-orange-900 ml-2">{pendingTransactions.length}</span>
                    </div>
                    <div>
                      <span className="text-orange-700">Total Amount:</span>
                      <span className="font-semibold text-orange-900 ml-2">
                        {formatCurrency(pendingTransactions.reduce((sum, item) => Number(sum) + Number(item.final_net_amount), 0))}
                      </span>
                    </div>
                    <div>
                      <span className="text-orange-700">Merchants:</span>
                      <span className="font-semibold text-orange-900 ml-2">
                        {new Set(pendingTransactions.map(item => item.merchant_vat)).size}
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

        </div>

        {/* Confirmation Modal */}
        {showConfirmModal && confirmationData && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl">
              <div className="flex items-center mb-4">
                <div className="flex-shrink-0">
                  <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                    <svg className="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  </div>
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    Confirm Bulk Approval
                  </h3>
                </div>
              </div>

              <div className="mb-6">
                <p className="text-sm text-gray-600 mb-3">
                  You are about to approve <strong>{confirmationData.count}</strong> selected transaction{confirmationData.count > 1 ? 's' : ''} from{' '}
                  <strong>{confirmationData.tab === 'today' ? "Today's Transactions" : "Pending Transactions"}</strong>.
                </p>
                
                {/* Enhanced workflow information */}
                {/* <div className="bg-blue-50 border border-blue-200 rounded-md p-3 mb-3">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-blue-700">
                        <strong>Enhanced Workflow:</strong> This will create a Payment Form with transaction number <strong>{trxNumber || 'Next Available'}</strong>, 
                        link all selected transactions, and mark them as transferred.
                      </p>
                    </div>
                  </div>
                </div> */}

                <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-yellow-700">
                        <strong>Warning:</strong> This action will create a permanent payment form record and cannot be easily undone.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={cancelBulkApproval}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                >
                  Cancel
                </button>
                <button
                  onClick={executeBulkApproval}
                  disabled={bulkApproving}
                  className="px-4 py-2 text-sm font-medium text-white bg-orange-600 border border-transparent rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {/* {bulkApproving ? 'Creating Payment Form...' : `Create Payment Form & Approve ${confirmationData.count} Transaction${confirmationData.count > 1 ? 's' : ''}`} */}
                  {bulkApproving ? 'Creating Payment Form...' : `Create Payment Form & Approve`}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Transaction Log Search Modal */}
        <TransactionLogSearchModal
          isOpen={showSearchModal}
          onClose={() => setShowSearchModal(false)}
          onSelectTransaction={(transaction) => {
            console.log('Selected transaction from search:', transaction);
            showNotification(`Selected transaction: ${transaction.trx_no}`, 'info');
            setShowSearchModal(false);
          }}
        />
      </div>
    </div>
  );
}

