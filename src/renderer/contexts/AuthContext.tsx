import { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react';
import { safeIpcInvoke } from '../utils/electron';

// Types
interface User {
  user_id: number;
  user_ref: string;
  user_name: string;
  role_code?: string;
  role_name?: string;
  last_login?: Date;
}

interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  sessionId: string | null;
  expiresAt: Date | null;
  isLoading: boolean;
}

interface LoginCredentials {
  username: string;
  password: string;
  force_login?: boolean;
}

interface LoginResult {
  success: boolean;
  message: string;
  error?: string;
}

interface AuthContextType extends AuthState {
  login: (credentials: LoginCredentials) => Promise<LoginResult>;
  logout: () => Promise<void>;
  validateSession: () => Promise<boolean>;
  clearAuth: () => void;
  getTimeUntilExpiry: () => number | null;
  extendSession: () => Promise<boolean>;
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Storage keys
const STORAGE_KEYS = {
  USER: 'auth_user',
  SESSION_ID: 'auth_session_id',
  EXPIRES_AT: 'auth_expires_at'
};

// Auth Provider Component
interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    user: null,
    sessionId: null,
    expiresAt: null,
    isLoading: true
  });

  // Session monitoring interval
  const [sessionMonitorInterval, setSessionMonitorInterval] = useState<NodeJS.Timeout | null>(null);

  // Auto-logout timer
  const [autoLogoutTimer, setAutoLogoutTimer] = useState<NodeJS.Timeout | null>(null);

  // Get client info for logging
  const getClientInfo = async () => {
    let ip_address = '127.0.0.1'; // Default to localhost
    try {
      const result = await safeIpcInvoke('get-ip-address');
      if (result && result.ip_address) {
        ip_address = result.ip_address;
      }
    } catch (error) {
      console.error('Failed to get IP address from main process:', error);
    }
    return {
      ip_address,
      user_agent: navigator.userAgent
    };
  };

  // Load auth state from localStorage on mount
  useEffect(() => {
    const loadAuthState = async () => {
      try {
        const storedUser = localStorage.getItem(STORAGE_KEYS.USER);
        const storedSessionId = localStorage.getItem(STORAGE_KEYS.SESSION_ID);
        const storedExpiresAt = localStorage.getItem(STORAGE_KEYS.EXPIRES_AT);

        if (storedUser && storedSessionId && storedExpiresAt) {
          const user = JSON.parse(storedUser);
          const expiresAt = new Date(storedExpiresAt);

          // Check if session is still valid
          if (new Date() < expiresAt) {
            // Validate session with server
            const isValid = await validateSessionWithServer(user.user_id, storedSessionId);
            
            if (isValid) {
              setAuthState({
                isAuthenticated: true,
                user,
                sessionId: storedSessionId,
                expiresAt,
                isLoading: false
              });
              
              // Set up auto-logout timer
              setupAutoLogout(expiresAt);

              // Start session monitoring
              startSessionMonitoring();
              return;
            }
          }
        }

        // Clear invalid/expired session
        clearAuthState();
      } catch (error) {
        console.error('Error loading auth state:', error);
        clearAuthState();
      }
    };

    loadAuthState();
  }, []);

  // Cleanup session monitoring on unmount
  useEffect(() => {
    return () => {
      stopSessionMonitoring();
    };
  }, []);

  // Validate session with server
  const validateSessionWithServer = async (userId: number, sessionId: string): Promise<boolean> => {
    try {
      const result = await safeIpcInvoke('validate-session', { user_id: userId, session_id: sessionId });
      return result.valid;
    } catch (error) {
      console.error('Session validation error:', error);
      return false;
    }
  };

  // Setup auto-logout timer with immediate check
  const setupAutoLogout = (expiresAt: Date) => {
    // Clear any existing auto-logout timer
    if (autoLogoutTimer) {
      clearTimeout(autoLogoutTimer);
      setAutoLogoutTimer(null);
    }

    const timeUntilExpiry = expiresAt.getTime() - Date.now();

    // If already expired, logout immediately
    if (timeUntilExpiry <= 0) {
      console.log('🔴 Session already expired - immediate logout');
      logout();
      return;
    }

    // Set timeout for exact expiry time
    const timer = setTimeout(() => {
      console.log('🔴 Session expired - auto-logout timer triggered');
      logout();
    }, timeUntilExpiry);

    setAutoLogoutTimer(timer);
    console.log(`⏰ Auto-logout timer set for ${Math.round(timeUntilExpiry / 1000)} seconds`);
  };

  // Start session monitoring with frequent expiry checks
  const startSessionMonitoring = () => {
    // Clear any existing interval
    if (sessionMonitorInterval) {
      clearInterval(sessionMonitorInterval);
    }

    // Check session every 30 seconds for expiry, every 5 minutes for server validation
    let serverCheckCounter = 0;
    const interval = setInterval(async () => {
      if (!authState.isAuthenticated || !authState.user || !authState.sessionId) {
        console.log('Session monitor: No active session, stopping monitoring');
        stopSessionMonitoring();
        return;
      }

      // Check if session is expired locally (every 30 seconds)
      if (authState.expiresAt && new Date() >= authState.expiresAt) {
        console.log('🔴 Session monitor: Session expired locally, logging out');
        await logout();
        return;
      }

      // Server validation every 5 minutes (10 cycles of 30 seconds)
      serverCheckCounter++;
      if (serverCheckCounter >= 10) {
        serverCheckCounter = 0;
        console.log('Session monitor: Checking session validity with server...');

        try {
          const isValid = await validateSessionWithServer(authState.user.user_id, authState.sessionId);
          if (!isValid) {
            console.log('Session monitor: Server validation failed, logging out');
            await logout();
          } else {
            console.log('Session monitor: Session is valid');
          }
        } catch (error) {
          console.error('Session monitor: Error validating session:', error);
          // Don't logout on network errors, just log the error
        }
      }
    }, 30 * 1000); // Check every 30 seconds

    setSessionMonitorInterval(interval);
    console.log('Session monitor: Started monitoring session (30s expiry check, 5min server validation)');
  };

  // Stop session monitoring
  const stopSessionMonitoring = () => {
    if (sessionMonitorInterval) {
      clearInterval(sessionMonitorInterval);
      setSessionMonitorInterval(null);
      console.log('Session monitor: Stopped monitoring session');
    }
  };

  // Clear auth state
  const clearAuthState = () => {
    // Stop session monitoring
    stopSessionMonitoring();

    // Clear auto-logout timer
    if (autoLogoutTimer) {
      clearTimeout(autoLogoutTimer);
      setAutoLogoutTimer(null);
    }

    localStorage.removeItem(STORAGE_KEYS.USER);
    localStorage.removeItem(STORAGE_KEYS.SESSION_ID);
    localStorage.removeItem(STORAGE_KEYS.EXPIRES_AT);

    setAuthState({
      isAuthenticated: false,
      user: null,
      sessionId: null,
      expiresAt: null,
      isLoading: false
    });
  };

  // Login function
  const login = async (credentials: LoginCredentials): Promise<LoginResult> => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true }));

      const clientInfo = await getClientInfo();
      console.log("clientInfo",clientInfo)
      const result = await safeIpcInvoke('user-login', {
        username: credentials.username,
        password: credentials.password,
        force_login: credentials.force_login,
        ...clientInfo
      });

      if (result.success) {
        const expiresAt = new Date(result.expires_at);
        
        // Store auth data
        localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(result.user));
        localStorage.setItem(STORAGE_KEYS.SESSION_ID, result.session_id);
        localStorage.setItem(STORAGE_KEYS.EXPIRES_AT, expiresAt.toISOString());

        // Update state
        console.log('AuthContext: Setting authenticated state', result.user);
        setAuthState({
          isAuthenticated: true,
          user: result.user,
          sessionId: result.session_id,
          expiresAt,
          isLoading: false
        });
        console.log('AuthContext: Authentication state updated');

        // Setup auto-logout
        setupAutoLogout(expiresAt);

        // Start session monitoring
        startSessionMonitoring();

        return { success: true, message: result.message };
      } else {
        setAuthState(prev => ({ ...prev, isLoading: false }));
        return { 
          success: false, 
          message: result.message,
          error: result.error 
        };
      }
    } catch (error: any) {
      setAuthState(prev => ({ ...prev, isLoading: false }));
      console.error('Login error:', error);
      return { success: false, message: 'Login failed due to connection error' };
    }
  };

  // Logout function
  const logout = async (): Promise<void> => {
    try {
      if (authState.user && authState.sessionId) {
        const clientInfo = getClientInfo();
        await safeIpcInvoke('user-logout', {
          user_id: authState.user.user_id,
          session_id: authState.sessionId,
          ...clientInfo
        });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      clearAuthState();
    }
  };

  // Expose logout function globally for main process access
  useEffect(() => {
    if (typeof window !== 'undefined') {
      (window as any).authLogout = logout;
    }

    return () => {
      if (typeof window !== 'undefined') {
        delete (window as any).authLogout;
      }
    };
  }, [logout]);

  // Validate current session
  const validateSession = useCallback(async (): Promise<boolean> => {
    if (!authState.user || !authState.sessionId) {
      return false;
    }

    try {
      const isValid = await validateSessionWithServer(authState.user.user_id, authState.sessionId);

      if (!isValid) {
        clearAuthState();
      }

      return isValid;
    } catch (error) {
      console.error('Session validation error:', error);
      clearAuthState();
      return false;
    }
  }, [authState.user, authState.sessionId]);

  // Clear auth (for manual logout)
  const clearAuth = () => {
    clearAuthState();
  };

  // Get time until session expiry in milliseconds
  const getTimeUntilExpiry = (): number | null => {
    if (!authState.expiresAt) {
      return null;
    }
    return authState.expiresAt.getTime() - Date.now();
  };

  // Extend session by validating and refreshing
  const extendSession = async (): Promise<boolean> => {
    if (!authState.user || !authState.sessionId) {
      return false;
    }

    try {
      const result = await validateSessionWithServer(authState.user.user_id, authState.sessionId);
      if (result) {
        // If session is still valid, extend the expiry time by 15 minutes (server policy)
        const newExpiresAt = new Date(Date.now() + 15 * 60 * 1000);

        // Update localStorage
        localStorage.setItem(STORAGE_KEYS.EXPIRES_AT, newExpiresAt.toISOString());

        // Update state
        setAuthState(prev => ({
          ...prev,
          expiresAt: newExpiresAt
        }));

        // Setup new auto-logout timer with server's 15-minute policy
        setupAutoLogout(newExpiresAt);

        // Restart session monitoring to ensure it uses the new expiry time
        startSessionMonitoring();

        console.log('✅ Session extended successfully until:', newExpiresAt);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error extending session:', error);
      return false;
    }
  };

  const contextValue: AuthContextType = {
    ...authState,
    login,
    logout,
    validateSession,
    clearAuth,
    getTimeUntilExpiry,
    extendSession
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

// Hook to use auth context
export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Hook to check if user has specific role
export function useRole(requiredRole?: string): boolean {
  const { user } = useAuth();
  
  if (!requiredRole || !user) {
    return true;
  }
  
  return user.role_code === requiredRole;
}

// Hook to check if user is admin
export function useIsAdmin(): boolean {
  return useRole('admin');
}
