// Constants and default data for Payment Request Form

export interface PaymentRequestData {
  date?: string;
  amount?: string;
  fromAccount?: {
    name?: string;
    type?: string;
    bank?: string;
    branch?: string;
    accountNo?: string;
  };
  transactions?: {
    merchantGross?: string;
    merchantGrossOth?: string;
    merchantNet?: string;
    discountEarned?: string;
    vat?: string;
    withholdingTax?: string; // Optional field for withholding tax
  };
  description?: string;
  dateRange?: {
    transfer_dt_bbl?: string; // BBL bank invoice date
    transfer_dt_oth?: string; // Other banks invoice date
  };
  summary?: {
    directCreditBBL?: SummarySection;
    mediaClearing?: SummarySection;
    sumTotal?: SummarySection;
  };
}

interface SummarySection {
  totalTransactions?: string;
  totalAmount?: string;
  lessDiscount?: string;
  transferFee?: string;
  vat?: string;
  netAmount?: string;
}

export const getDefaultData = (companySettings?: any): PaymentRequestData => ({
  date: "22 Jul 2025",
  amount: "757,496.52",
  fromAccount: {
    name: companySettings?.company_name_en || "E-POS Service Company Limited",
    type: "Saving Account",
    bank: companySettings?.bank_name || "BBL",
    branch: companySettings?.branch || "Head Office",
    accountNo: companySettings?.ac_no || "224-0-33223-5",
  },
  transactions: {
    merchantGross: "755,987.00",
    merchantGrossOth: "8,880.85",
    merchantNet: "8,880.85",
    discountEarned: "6,889.10",
    vat: "482.23",
  },
  dateRange: {
    transfer_dt_bbl: "22 Jul 2025",
    transfer_dt_oth: "24 Jul 2025",
  },
  summary: {
    directCreditBBL: {
      totalTransactions: "31",
      totalAmount: "755,987.00",
      lessDiscount: "6,803.89",
      transferFee: "0.00",
      vat: "476.27",
      netAmount: "748,706.84"
    },
    mediaClearing: {
      totalTransactions: "5",
      totalAmount: "8,880.85",
      lessDiscount: "85.21",
      transferFee: "0.00",
      vat: "5.96",
      netAmount: "8,789.68"
    },
    sumTotal: {
      totalTransactions: "36",
      totalAmount: "764,867.85",
      lessDiscount: "6,889.10",
      transferFee: "0.00",
      vat: "482.23",
      netAmount: "757,496.52"
    }
  },
});

export const TRANSACTION_ROWS = [
  { label: "Dr. Merchant (Gross)", field: "merchantGross" },
  { label: "Dr. Merchant (Gross)", field: "merchantGrossOth" },
  { label: "Bank To Merchant", field: "merchantNet" },
  { label: "MDR Revenue", field: "discountEarned" },
  { label: "Transfer Fee Revenue", field: null, value: "0.00" },
  { label: "Cr. Vat 7%", field: "vat" },
] as const;

export const SUMMARY_ROWS = [
  { label: "Total Amount(Trx.)", field: "totalTransactions", hasUnderline: false, isBold: false },
  { label: "Total Amount(Baht.)", field: "totalAmount", hasUnderline: true, isBold: false },
  { label: "MDR Revenue", field: "lessDiscount", hasUnderline: true, isBold: false },
  { label: "Transfer Fee Revenue", field: "transferFee", hasUnderline: true, isBold: false },
  { label: "VAT 7%", field: "vat", hasUnderline: true, isBold: false },
  { label: "Net Amount", field: "netAmount", hasUnderline: true, isBold: true },
] as const;

export const SUMMARY_COLUMNS = [
  { label: "Direct Credit BBL", field: "directCreditBBL" },
  { label: "Media Clearing", field: "mediaClearing" },
  { label: "Sum Total", field: "sumTotal" },
] as const;
