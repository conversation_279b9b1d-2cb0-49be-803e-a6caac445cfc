import React, { useState, useEffect } from 'react';
import { Modal } from './modal';
import { Button } from './button';
import { useAuth } from '../contexts/AuthContext';
import { useNotification } from '../contexts/NotificationContext';
import { safeIpcInvoke } from '../utils/electron';

interface Transaction {
  id: number;
  transaction_id: string;
  transaction_amount: number;
  transaction_merchant_id?: string;
  transaction_merchant_vat?: string;
  transaction_merchant_name?: string;
  transaction_card_no?: string;
  transaction_trade_type?: string;
  transaction_channel_type?: string;
  transaction_time: string;
  reference_no?: string;
}

interface FormAdjustmentRequestModalProps {
  isOpen: boolean;
  onClose: () => void;
  transaction: Transaction | null;
  onSuccess?: () => void;
}

interface AdjustmentFormData {
  disputed_amount: number;
  disputed_full: number;
  disputed_type: string;
  currency_code: string;
  support_document: boolean;
  cdrs: string;
  card_type: string;
  reason: string;
}

export function FormAdjustmentRequestModal({
  isOpen,
  onClose,
  transaction,
  onSuccess
}: FormAdjustmentRequestModalProps) {
  const { user } = useAuth();
  const { showNotification } = useNotification();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<AdjustmentFormData>({
    disputed_amount: 0,
    disputed_full: 1,
    disputed_type: 'Credit Adjustment',
    currency_code: 'THB',
    support_document: true,
    cdrs: 'Normal',
    card_type: '',
    reason: 'Refund diff amount to customer.'
  });

  // Pre-populate form when transaction changes
  useEffect(() => {
    if (transaction) {
      setFormData(prev => ({
        ...prev,
        disputed_amount: transaction.transaction_amount,
        card_type: transaction.transaction_card_no || '',
        reason: 'Refund diff amount to customer.'
      }));
    }
  }, [transaction]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  };

  const handleInputChange = (field: keyof AdjustmentFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!transaction || !user) {
      showNotification('Missing transaction or user information', 'error');
      return;
    }

    if (formData.disputed_amount <= 0) {
      showNotification('Disputed amount must be greater than 0', 'error');
      return;
    }

    setIsSubmitting(true);

    try {
      const adjustmentData = {
        disputed_amount: formData.disputed_amount,
        disputed_full: formData.disputed_full,
        disputed_type: formData.disputed_type || null,
        currency_code: formData.currency_code,
        support_document: formData.support_document,
        cdrs: formData.cdrs || null,
        card_type: formData.card_type || null,
        reason: formData.reason,
        transaction_ref: transaction.transaction_id,
        transaction_id: transaction.transaction_id,
        merchant_vat: transaction.transaction_merchant_vat,
        merchant_id: transaction.transaction_merchant_id,
        transaction_channel_type: transaction.transaction_channel_type,
        transaction_transaction_time: transaction.transaction_time,
        create_by: user.user_name
      };

      const result = await safeIpcInvoke('create-transaction-adjustment-request', adjustmentData);

      if (result.success) {
        showNotification('Transaction adjustment request created successfully', 'success');
        onSuccess?.();
        onClose();
        
        // Reset form
        setFormData({
          disputed_amount: 0,
          disputed_full: 1,
          disputed_type: 'Credit Adjustment',
          currency_code: 'THB',
          support_document: true,
          cdrs: 'Normal',
          card_type: '',
          reason: 'Refund diff amount to customer.'
        });
      } else {
        showNotification(result.message || 'Failed to create adjustment request', 'error');
      }
    } catch (error) {
      console.error('Error creating adjustment request:', error);
      showNotification('An error occurred while creating the adjustment request', 'error');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  if (!transaction) {
    return null;
  }

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="xl">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between border-b border-gray-200 pb-4">
          <div className="flex items-center gap-2">
            <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            <h3 className="text-xl font-semibold text-gray-900">Form Adjustment Request</h3>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Transaction Information Section */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="text-blue-800 font-semibold mb-3 border-b border-blue-200 pb-2">
              Transaction Information
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Merchant ID <span className="text-red-500">*</span>
                </label>
                <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm">
                  {transaction.transaction_merchant_id || 'N/A'}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Merchant Name <span className="text-red-500">*</span>
                </label>
                <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm">
                  {transaction.transaction_merchant_name || 'N/A'}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Card Number <span className="text-red-500">*</span>
                </label>
                <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm">
                  {transaction.transaction_card_no || 'N/A'}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  MDR
                </label>
                <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm">
                  0.90
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Transaction Date <span className="text-red-500">*</span>
                </label>
                <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm">
                  {formatDate(transaction.transaction_time)}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Bank account
                </label>
                <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm">
                  ********** - 002
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Withholding Tax
                </label>
                <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm">
                  3.00
                </div>
              </div>
            </div>

            {/* Second Row */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Currency Code
                </label>
                <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm">
                  THB
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Reference Number
                </label>
                <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm">
                  {transaction.reference_no || 'N/A'}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Merchant Vat
                </label>
                <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm">
                  {transaction.transaction_merchant_vat || 'N/A'}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Transaction Amount
                </label>
                <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm text-right">
                  {transaction.transaction_amount?.toLocaleString('th-TH', { minimumFractionDigits: 2 }) || '0.00'}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Channel Type
                </label>
                <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm">
                  {transaction.transaction_channel_type || 'N/A'}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Terminal ID
                </label>
                <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm">
                  33173516
                </div>
              </div>
            </div>
          </div>

          {/* Dispute Information Section */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="text-blue-800 font-semibold mb-3 border-b border-blue-200 pb-2">
              Dispute Information
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Disputed Amount <span className="text-red-500">*</span>
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.disputed_amount}
                  onChange={(e) => handleInputChange('disputed_amount', parseFloat(e.target.value) || 0)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm text-right"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Currency Code
                </label>
                <select
                  value={formData.currency_code}
                  onChange={(e) => handleInputChange('currency_code', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                >
                  <option value="THB">THB</option>
                </select>
              </div>
            </div>

            {/* Radio Button Groups */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
              <div>
                <div className="space-y-2">
                  <div className="flex items-center">
                    <input
                      id="full-amount"
                      name="amount-type"
                      type="radio"
                      checked={formData.disputed_full === 1}
                      onChange={() => handleInputChange('disputed_full', 1)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                    />
                    <label htmlFor="full-amount" className="ml-2 text-sm text-gray-700">
                      Full Amount
                    </label>
                  </div>
                  <div className="flex items-center">
                    <input
                      id="partial-amount"
                      name="amount-type"
                      type="radio"
                      checked={formData.disputed_full === 0}
                      onChange={() => handleInputChange('disputed_full', 0)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                    />
                    <label htmlFor="partial-amount" className="ml-2 text-sm text-gray-700">
                      Partial Amount
                    </label>
                  </div>
                </div>

                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Dispute Resolution Type
                  </label>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <input
                        id="credit-adjustment"
                        name="resolution-type"
                        type="radio"
                        checked={formData.disputed_type === 'Credit Adjustment'}
                        onChange={() => handleInputChange('disputed_type', 'Credit Adjustment')}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                      />
                      <label htmlFor="credit-adjustment" className="ml-2 text-sm text-gray-700">
                        Credit Adjustment
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        id="first-chargeback"
                        name="resolution-type"
                        type="radio"
                        checked={formData.disputed_type === 'First Chargeback'}
                        onChange={() => handleInputChange('disputed_type', 'First Chargeback')}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                      />
                      <label htmlFor="first-chargeback" className="ml-2 text-sm text-gray-700">
                        First Chargeback
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        id="debit-adjustment"
                        name="resolution-type"
                        type="radio"
                        checked={formData.disputed_type === 'Debit Adjustment'}
                        onChange={() => handleInputChange('disputed_type', 'Debit Adjustment')}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                      />
                      <label htmlFor="debit-adjustment" className="ml-2 text-sm text-gray-700">
                        Debit Adjustment (Credit Card)
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        id="representment"
                        name="resolution-type"
                        type="radio"
                        checked={formData.disputed_type === 'Representment'}
                        onChange={() => handleInputChange('disputed_type', 'Representment')}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                      />
                      <label htmlFor="representment" className="ml-2 text-sm text-gray-700">
                        Representment (Credit Card)
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        id="second-chargeback"
                        name="resolution-type"
                        type="radio"
                        checked={formData.disputed_type === 'Second Chargeback'}
                        onChange={() => handleInputChange('disputed_type', 'Second Chargeback')}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                      />
                      <label htmlFor="second-chargeback" className="ml-2 text-sm text-gray-700">
                        Second Chargeback (Credit Card Non-ATM)
                      </label>
                    </div>
                  </div>
                </div>

                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Supporting Documents
                  </label>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <input
                        id="supporting-docs-yes"
                        name="supporting-documents"
                        type="radio"
                        checked={formData.support_document === true}
                        onChange={() => handleInputChange('support_document', true)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                      />
                      <label htmlFor="supporting-docs-yes" className="ml-2 text-sm text-gray-700">
                        Yes
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        id="supporting-docs-no"
                        name="supporting-documents"
                        type="radio"
                        checked={formData.support_document === false}
                        onChange={() => handleInputChange('support_document', false)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                      />
                      <label htmlFor="supporting-docs-no" className="ml-2 text-sm text-gray-700">
                        No
                      </label>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      CDRS
                    </label>
                    <div className="space-y-2">
                      <div className="flex items-center">
                        <input
                          id="cdrs-normal"
                          name="cdrs"
                          type="radio"
                          checked={formData.cdrs === 'Normal'}
                          onChange={() => handleInputChange('cdrs', 'Normal')}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                        />
                        <label htmlFor="cdrs-normal" className="ml-2 text-sm text-blue-600">
                          Normal
                        </label>
                      </div>
                      <div className="flex items-center">
                        <input
                          id="cdrs-show-only"
                          name="cdrs"
                          type="radio"
                          checked={formData.cdrs === 'Show only'}
                          onChange={() => handleInputChange('cdrs', 'Show only')}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                        />
                        <label htmlFor="cdrs-show-only" className="ml-2 text-sm text-gray-700">
                          Show only
                        </label>
                      </div>
                      <div className="flex items-center">
                        <input
                          id="cdrs-do-not-show"
                          name="cdrs"
                          type="radio"
                          checked={formData.cdrs === 'Do not show'}
                          onChange={() => handleInputChange('cdrs', 'Do not show')}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                        />
                        <label htmlFor="cdrs-do-not-show" className="ml-2 text-sm text-gray-700">
                          Do not show
                        </label>
                      </div>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Card Type
                    </label>
                    <textarea
                      rows={2}
                      value={formData.card_type}
                      onChange={(e) => handleInputChange('card_type', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Additional Message Section */}
            <div className="grid grid-cols-1 gap-6 mt-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Additional Message
                </label>
                <textarea
                  rows={4}
                  value={formData.reason}
                  onChange={(e) => handleInputChange('reason', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                  placeholder="Refund diff amount to customer."
                />
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-center gap-4 pt-6 border-t border-gray-200">
            <Button
              type="button"
              variant="secondary"
              className="flex items-center gap-2 px-6 py-3"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>

            <Button
              type="submit"
              variant="primary"
              className="flex items-center gap-2 px-6 py-3"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Submitting...
                </>
              ) : (
                <>
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                  บันทึก (Save)
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </Modal>
  );
}
