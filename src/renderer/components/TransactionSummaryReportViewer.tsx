import React, { useState } from 'react';
import { PDFViewer, pdf } from '@react-pdf/renderer';
import { TransactionSummaryReport } from './TransactionSummaryReport';
import { ReportSummary } from '../services/reportService';
import { X, Download, RefreshCw, FileSpreadsheet } from 'lucide-react';
import * as XLSX from 'xlsx';

interface TransactionSummaryReportViewerProps {
  isOpen: boolean;
  onClose: () => void;
  reportData: ReportSummary | null;
  isLoading?: boolean;
  title?: string;
}

export const TransactionSummaryReportViewer: React.FC<TransactionSummaryReportViewerProps> = ({
  isOpen,
  onClose,
  reportData,
  isLoading = false,
  title = "Transaction Summary Report"
}) => {
  const [downloading, setDownloading] = useState(false);
  const [exportingExcel, setExportingExcel] = useState(false);

  // Handle download of transaction summary report
  const handleDownloadReport = async () => {
    if (!reportData) return;

    setDownloading(true);
    try {
      console.log('📥 Generating transaction summary report PDF for download...');
      const blob = await pdf(<TransactionSummaryReport reportData={reportData} />).toBlob();
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `transaction-summary-report-${reportData.reportDate.replace(/\//g, '-')}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      console.log('✅ Transaction summary report PDF download initiated successfully');
    } catch (error) {
      console.error('❌ Error downloading transaction summary report:', error);
    } finally {
      setDownloading(false);
    }
  };

  // Handle Excel export of transaction summary report
  const handleExportToExcel = async () => {
    if (!reportData) return;

    setExportingExcel(true);
    try {
      console.log('📊 Generating transaction summary report Excel for download...');

      // Import ReportService to use the same data structure as PDF
      const { ReportService } = await import('../services/reportService');

      // Use the same data structure as PDF - this groups merchants by bank and adds subtotals
      const dateGroupedData = ReportService.generateDateGroupedData(reportData.merchants);

      // Prepare summary data
      const summaryData = [];

      // Add header information
      summaryData.push({
        'Report Information': 'Transaction Summary Report',
        'Value': reportData.reportDate,
        'Details': '',
        'Amount': ''
      });
      summaryData.push({
        'Report Information': 'Report Time',
        'Value': reportData.reportTime,
        'Details': '',
        'Amount': ''
      });
      summaryData.push({
        'Report Information': 'Total Merchants',
        'Value': Array.from(new Set(reportData.merchants.map(m => m.merchantVat))).length,
        'Details': '',
        'Amount': ''
      });
      summaryData.push({
        'Report Information': 'Total Transactions',
        'Value': reportData.grandTotals.totalTransactions,
        'Details': '',
        'Amount': ''
      });
      
      // Add empty row
      summaryData.push({});

      // Add column headers
      summaryData.push({
        'Report Information': 'Transaction Date',
        'Value': 'Merchant Name',
        'Details': 'MDR %',
        'Amount': 'Transaction Count',
        'Transaction Amount': 'Transaction Amount',
        'MDR Amount': 'MDR Amount',
        'Transfer Fee': 'Transfer Fee', 
        'VAT Amount': 'VAT Amount',
        'Withholding Tax': 'Withholding Tax',
        'Net Amount': 'Net Amount',
        'Reimburse Fee': 'Reimburse Fee',
        'Service Fee': 'Service Fee',
        'Business Tax': 'Business Tax',
        'Final Net Amount': 'Final Net Amount',
        'Channel Type': 'Channel Type'
      });

      // Process the same data structure as PDF
      dateGroupedData.forEach(item => {
        if (item.isSubtotal) {
          // Handle subtotal rows (bank subtotals, empty rows, grand total)
          if (item.subtotalType === 'bank') {
            // Add empty row before bank subtotal for spacing
            summaryData.push({});
            
            // Bank subtotal row
            summaryData.push({
              'Report Information': '',
              'Value': `${item.bankCode} Total`,
              'Details': '',
              'Amount': item.totalTransactions,
              'Transaction Amount': (item.totalAmount || 0).toFixed(2),
              'MDR Amount': (item.totalMdrAmount || 0).toFixed(2),
              'Transfer Fee': (item.totalTransferFee || 0).toFixed(2),
              'VAT Amount': (item.totalVatAmount || 0).toFixed(2),
              'Withholding Tax': (item.totalWithholdTax || 0).toFixed(2),
              'Net Amount': (item.totalNetAmount || 0).toFixed(2),
              'Reimburse Fee': (item.totalReimbursementFee || 0).toFixed(2),
              'Service Fee': (item.totalServiceFee || 0).toFixed(2),
              'Business Tax': (item.totalBusinessTax || 0).toFixed(2),
              'Final Net Amount': (item.totalFinalNetAmount || 0).toFixed(2),
              'Channel Type': ''
            });
          } else if (item.subtotalType === 'empty') {
            // Empty row for spacing
            summaryData.push({});
          } else if (item.subtotalType === 'grand') {
            // Grand total row
            summaryData.push({
              'Report Information': '',
              'Value': 'Total :',
              'Details': `${reportData.grandTotals.averageMdrRate.toFixed(2)}%`,
              'Amount': item.totalTransactions,
              'Transaction Amount': (item.totalAmount || 0).toFixed(2),
              'MDR Amount': (item.totalMdrAmount || 0).toFixed(2),
              'Transfer Fee': (item.totalTransferFee || 0).toFixed(2),
              'VAT Amount': (item.totalVatAmount || 0).toFixed(2),
              'Withholding Tax': (item.totalWithholdTax || 0).toFixed(2),
              'Net Amount': (item.totalNetAmount || 0).toFixed(2),
              'Reimburse Fee': (item.totalReimbursementFee || 0).toFixed(2),
              'Service Fee': (item.totalServiceFee || 0).toFixed(2),
              'Business Tax': (item.totalBusinessTax || 0).toFixed(2),
              'Final Net Amount': (item.totalFinalNetAmount || 0).toFixed(2),
              'Channel Type': ''
            });
          }
        } else {
          // Regular merchant row
          summaryData.push({
            'Report Information': item.transactionDate,
            'Value': item.merchantName + (item.tradeStatus === 'REFUND' ? ' (Refund)' : ''),
            'Details': item.mdrRate.toFixed(2) + '%',
            'Amount': item.transactionCount,
            'Transaction Amount': item.totalAmount.toFixed(2),
            'MDR Amount': item.mdrAmount.toFixed(2),
            'Transfer Fee': item.transferFee.toFixed(2),
            'VAT Amount': item.vatAmount.toFixed(2),
            'Withholding Tax': item.withholdTax.toFixed(2),
            'Net Amount': item.netAmount.toFixed(2),
            'Reimburse Fee': item.reimbursementFee.toFixed(2),
            'Service Fee': item.serviceFee.toFixed(2),
            'Business Tax': item.cupBusinessTaxFee.toFixed(2),
            'Final Net Amount': item.finalNetAmount.toFixed(2),
            'Channel Type': item.channelType
          });
        }
      });

      // Create workbook and worksheet
      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.json_to_sheet(summaryData);

      // Set column widths for better readability
      const columnWidths = [
        { wch: 15 }, // Report Information / Transaction Date
        { wch: 35 }, // Value / Merchant Name
        { wch: 12 }, // Details / MDR %
        { wch: 15 }, // Amount / Transaction Count
        { wch: 18 }, // Transaction Amount
        { wch: 15 }, // MDR Amount
        { wch: 15 }, // Transfer Fee
        { wch: 15 }, // VAT Amount
        { wch: 18 }, // Withholding Tax
        { wch: 15 }, // Net Amount
        { wch: 15 }, // Reimburse Fee
        { wch: 15 }, // Service Fee
        { wch: 15 }, // Business Tax
        { wch: 18 }, // Final Net Amount
        { wch: 15 }  // Channel Type
      ];
      worksheet["!cols"] = columnWidths;

      // Add worksheet to workbook
      XLSX.utils.book_append_sheet(workbook, worksheet, "Transaction Summary");

      // Generate filename with report date
      const defaultFilename = `transaction-summary-report-${reportData.reportDate.replace(/\//g, '-')}.xlsx`;

      // Use existing Electron API functions from transaction list screen
      const safeIpcInvoke = async (channel: string, ...args: any[]) => {
        try {
          if (window.electronAPI?.invoke) {
            return await window.electronAPI.invoke(channel, ...args);
          }
          throw new Error('IPC not available');
        } catch (error) {
          console.error(`IPC invoke error for ${channel}:`, error);
          throw error;
        }
      };

      // Show save dialog using existing IPC handlers
      const saveDialogResult = await safeIpcInvoke("show-save-dialog", {
        defaultPath: defaultFilename,
        filters: [
          { name: 'Excel Files', extensions: ['xlsx'] },
          { name: 'All Files', extensions: ['*'] }
        ]
      });

      if (saveDialogResult.success && !saveDialogResult.canceled && saveDialogResult.filePath) {
        // Generate Excel buffer
        const excelBuffer = XLSX.write(workbook, {
          type: 'buffer',
          bookType: 'xlsx'
        });

        // Save file using existing IPC handler
        const saveResult = await safeIpcInvoke(
          "save-excel-file",
          saveDialogResult.filePath,
          excelBuffer
        );
        
        if (saveResult.success) {
          console.log(`✅ Transaction Summary Excel export completed: ${saveResult.filePath}`);
          
          // Show success message - you might want to implement a toast notification system
          alert(`Excel file exported successfully!\nFile: ${saveResult.filePath}\nRecords: ${reportData.merchants.length} merchants`);
        } else {
          throw new Error(saveResult.error || 'Failed to save Excel file');
        }
      } else if (!saveDialogResult.canceled) {
        throw new Error('Save dialog failed');
      }

    } catch (error) {
      console.error('❌ Error exporting transaction summary to Excel:', error);
      alert('Failed to export Excel file. Please try again.');
    } finally {
      setExportingExcel(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 backdrop-blur-sm">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-[95vw] mx-4 h-[95vh] overflow-hidden flex flex-col border border-gray-200">
        {/* Header */}
        <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-700 text-white shadow-lg">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
              <span className="text-xl">📊</span>
            </div>
            <div>
              <h2 className="text-xl font-bold tracking-tight">{title}</h2>
              {reportData && (
                <div className="flex items-center space-x-4 mt-1">
                    <p className="text-sm text-blue-100">
                    <span className="font-medium">
                      {
                      Array.from(new Set(reportData.merchants.map(m => m.merchantVat))).length
                      }
                    </span> merchants
                    </p>
                  <span className="text-blue-300">•</span>
                  <p className="text-sm text-blue-100">
                    <span className="font-medium">{reportData.grandTotals.totalTransactions}</span> transactions
                  </p>
                  <span className="text-blue-300">•</span>
                  <p className="text-sm text-blue-100">
                    <span className="font-medium">₿{reportData.grandTotals.totalNetAmount.toLocaleString()}</span>
                  </p>
                </div>
              )}
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            {/* Download PDF Button */}
            <button
              onClick={handleDownloadReport}
              disabled={downloading || isLoading || !reportData}
              className="px-4 py-2 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 text-sm font-medium border border-white border-opacity-30"
              title="Download PDF Report"
            >
              {downloading ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <Download className="w-4 h-4" />
              )}
              <span>{downloading ? 'Downloading...' : 'Download PDF'}</span>
            </button>

            {/* Export Excel Button */}
            <button
              onClick={handleExportToExcel}
              disabled={exportingExcel || isLoading || !reportData}
              className="px-4 py-2 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 text-sm font-medium border border-white border-opacity-30"
              title="Export to Excel"
            >
              {exportingExcel ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <FileSpreadsheet className="w-4 h-4" />
              )}
              <span>{exportingExcel ? 'Exporting...' : 'Export Excel'}</span>
            </button>
            
            {/* Close Button */}
            <button
              onClick={onClose}
              className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-all duration-200 group"
              title="Close Report"
            >
              <X className="w-5 h-5 group-hover:rotate-90 transition-transform duration-200" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden bg-gray-50">
          {isLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center p-8">
                <div className="relative">
                  <div className="w-16 h-16 bg-blue-100 rounded-full mx-auto mb-6 flex items-center justify-center">
                    <RefreshCw className="w-8 h-8 animate-spin text-blue-600" />
                  </div>
                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin"></div>
                </div>
                <h3 className="text-lg font-semibold text-gray-800 mb-2">Generating Report</h3>
                <p className="text-gray-600">Please wait while we prepare your Transaction Summary Report...</p>
                <div className="mt-4 w-48 h-1 bg-gray-200 rounded-full mx-auto overflow-hidden">
                  <div className="h-full bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full animate-pulse"></div>
                </div>
              </div>
            </div>
          ) : reportData ? (
            <div className="h-full p-4">
              <div className="h-full bg-white rounded-lg shadow-inner border border-gray-200 overflow-hidden">
                <PDFViewer
                  style={{
                    width: '100%',
                    height: '100%',
                    border: 'none',
                    borderRadius: '8px'
                  }}
                  showToolbar={true}
                >
                  <TransactionSummaryReport reportData={reportData} />
                </PDFViewer>
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center p-8">
                <div className="w-20 h-20 bg-gray-100 rounded-full mx-auto mb-6 flex items-center justify-center">
                  <X className="w-10 h-10 text-gray-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-800 mb-2">No Report Data</h3>
                <p className="text-gray-600 mb-4">Unable to load the transaction summary report.</p>
                <button
                  onClick={onClose}
                  className="px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-lg transition-colors duration-200"
                >
                  Close
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TransactionSummaryReportViewer;
