import React, { useEffect, useState } from "react";
import {
  Document,
  Page,
  Text,
  View,
  pdf,
  Image,
} from "@react-pdf/renderer";

import logoImg from "../assets/E-POS-logo-1.png";
import { styles } from "./PaymentRequestForm/styles";
import { PaymentRequestData, getDefaultData } from "./PaymentRequestForm/constants";
import { TransferDetails } from "./PaymentRequestForm/TransferDetails";
import { TransactionTable } from "./PaymentRequestForm/TransactionTable";
import { SummaryTable } from "./PaymentRequestForm/SummaryTable";
import { SignatureSection } from "./PaymentRequestForm/SignatureSection";

// PDF Document Component
const PaymentRequestPDF: React.FC<{ data?: PaymentRequestData }> = ({ data }) => {
  // State for company settings
  const [companySettings, setCompanySettings] = useState<any>(null);
  const [loadingSettings, setLoadingSettings] = useState(true);

  // Fetch company settings when component mounts
  useEffect(() => {
    const fetchCompanySettings = async () => {
      try {
        if (window.electronAPI?.getCompanySettings) {
          const result = await window.electronAPI.getCompanySettings();
          if (result.success && result.data) {
            setCompanySettings(result.data);
          }
        }
      } catch (error) {
        console.error("Error fetching company settings:", error);
      } finally {
        setLoadingSettings(false);
      }
    };

    fetchCompanySettings();
  }, []);

  const formData = { ...getDefaultData(companySettings), ...data };
  console.log("formData",formData)
  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* Logo */}
        <View style={styles.logoContainer}>
          <Image style={styles.logo} src={logoImg} />
        </View>

        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>PAYMENT REQUEST FORM</Text>
          <Text style={styles.date}>{formData.date}</Text>
        </View>

        {/* Approval Text */}
        <View style={styles.approvalContainer}>
          <View style={styles.fromSection}>
            <Text style={styles.bold}>From </Text>
            <Text>: Operation Department - Settlement</Text>
          </View>
          <Text style={styles.approvalText}>
            Please approved to transfer amount to our merchant. The detail are as follows:-
          </Text>
        </View>

        {/* Amount Section */}
        <View style={styles.amountSection}>
          <View style={styles.amountRow}>
            <Text style={styles.amountLabel}>Amount :</Text>
            <Text style={styles.amountValue}>{formData.amount}</Text>
            <Text style={styles.amountLabel}>Baht.</Text>
          </View>
        </View>

        {/* Transfer Details */}
        <TransferDetails fromAccount={formData.fromAccount} />

        {/* Transaction Details Table */}
        <TransactionTable transactions={formData.transactions} />

        {/* Description */}
        <Text style={styles.descriptionText}>
          Description : With Holding Tax 3% Amount <Text style={styles.bold}>{formData.transactions?.withholdingTax}</Text> THB
        </Text>

        {/* Summary Table */}
        <SummaryTable summary={formData.summary} dateRange={formData.dateRange} />

        {/* Signature Section */}
        <SignatureSection />
      </Page>
    </Document>
  );
};

// Hook for generating PDF
export const usePaymentRequestPDF = () => {
  const generatePDF = async (data?: PaymentRequestData): Promise<Blob> => {
    const pdfDocument = <PaymentRequestPDF data={data} />;
    const pdfBlob = await pdf(pdfDocument).toBlob();
    return pdfBlob;
  };

  const downloadPDF = async (
    data?: PaymentRequestData,
    filename: string = "payment-request-form.pdf"
  ) => {
    try {
      const pdfBlob = await generatePDF(data);
      const url = URL.createObjectURL(pdfBlob);
      const link = document.createElement("a");
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error downloading PDF:", error);
      throw error;
    }
  };

  const openPDFInNewWindow = async (data?: PaymentRequestData) => {
    try {
      const pdfBlob = await generatePDF(data);
      const url = URL.createObjectURL(pdfBlob);
      window.open(url, "_blank");
    } catch (error) {
      console.error("Error opening PDF:", error);
      throw error;
    }
  };

  return {
    generatePDF,
    downloadPDF,
    openPDFInNewWindow,
  };
};

// Export the PDF component and types
export { PaymentRequestPDF };
export type { PaymentRequestData };
