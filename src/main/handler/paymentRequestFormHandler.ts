import { ipcMain, dialog } from 'electron';
import { promises as fs } from 'fs';
import path from 'path';

// PDF generation interfaces
interface PaymentRequestData {
  date?: string;
  amount?: string;
  fromAccount?: {
    name?: string;
    type?: string;
    bank?: string;
    branch?: string;
    accountNo?: string;
  };
  transactions?: {
    merchantGross?: string;
    merchantNet?: string;
    discountEarned?: string;
    vat?: string;
  };
  description?: string;
  dateRange?: {
    transfer_dt_bbl?: string; // BBL bank invoice date
    transfer_dt_oth?: string; // Other banks invoice date
  };
  mediaClearing?: {
    totalTransactions?: string;
    sumTotal?: string;
  };
  summary?: {
    transferDate?: string;
    totalAmount?: string;
    lessDiscount?: string;
    vatAmount?: string;
    netAmount?: string;
  };
}

interface PaymentRequestFormResponse {
  success: boolean;
  message: string;
  data?: any;
  error?: string;
}

// Helper function to generate HTML content for PDF
function generatePaymentRequestHTML(data: PaymentRequestData): string {
  // Default data if not provided
  const defaultData: PaymentRequestData = {
    date: '22 Jul 2025',
    amount: '757,496.52',
    fromAccount: {
      name: 'E-POS Service Company Limited',
      type: 'Saving Account',
      bank: 'BBL',
      branch: 'Head Office',
      accountNo: '224-0-33223-5',
    },
    transactions: {
      merchantGross: '755,987.00',
      merchantNet: '8,880.85',
      discountEarned: '6,889.10',
      vat: '482.23',
    },
    description: 'With Holding Tax 3% Amount Thb 204.52',
    dateRange: {
      transfer_dt_bbl: '22 Jul 2025',
      transfer_dt_oth: '24 Jul 2025',
    },
    mediaClearing: {
      totalTransactions: '36',
      sumTotal: '6,889.10',
    },
    summary: {
      transferDate: 'Direct Credit BBL',
      totalAmount: '757,496.52',
      lessDiscount: '748,706.84',
      vatAmount: '476.27',
      netAmount: '755,987.00',
    },
  };

  const formData = { ...defaultData, ...data };

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>Payment Request Form</title>
      <style>
        body {
          font-family: 'Helvetica', sans-serif;
          margin: 0;
          padding: 30px;
          background-color: #FFFFFF;
          color: #000;
          font-size: 12px;
        }
        .header {
          text-align: center;
          margin-bottom: 20px;
        }
        .title {
          font-size: 16px;
          font-weight: bold;
          letter-spacing: 2px;
          margin-bottom: 10px;
        }
        .date {
          font-size: 12px;
          margin-bottom: 5px;
        }
        .from-section {
          font-size: 12px;
          margin-bottom: 15px;
        }
        .approval-text {
          font-size: 11px;
          margin-bottom: 20px;
          line-height: 1.4;
        }
        .amount-section {
          margin-bottom: 15px;
        }
        .amount-label {
          font-size: 12px;
          font-weight: bold;
        }
        .amount-value {
          font-size: 14px;
          font-weight: bold;
          margin-left: 10px;
        }
        .table {
          width: 100%;
          border-collapse: collapse;
          border: 1px solid #000;
          margin-bottom: 20px;
        }
        .table th, .table td {
          border: 1px solid #000;
          padding: 5px;
          text-align: left;
        }
        .table th {
          background-color: #f0f0f0;
          font-weight: bold;
          font-size: 10px;
        }
        .table td {
          font-size: 10px;
        }
        .transaction-section {
          margin-bottom: 20px;
        }
        .transaction-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 3px;
          font-size: 10px;
        }
        .transaction-label {
          width: 60%;
        }
        .transaction-amount {
          width: 40%;
          text-align: right;
        }
        .summary-section {
          margin-top: 20px;
          margin-bottom: 30px;
        }
        .signature-section {
          display: flex;
          justify-content: space-between;
          margin-top: 40px;
        }
        .signature-box {
          width: 30%;
          text-align: center;
        }
        .signature-label {
          font-size: 10px;
          border-top: 1px solid #000;
          padding-top: 5px;
          margin-top: 50px;
        }
        .bold {
          font-weight: bold;
        }
        .center {
          text-align: center;
        }
      </style>
    </head>
    <body>
      <!-- Header -->
      <div class="header">
        <div class="title">PAYMENT REQUEST FORM 1</div>
        <div class="date">${formData.date}</div>
        <div class="from-section">From : Operation Department - Settlement</div>
      </div>

      <!-- Approval Text -->
      <div class="approval-text">
        Please approved to transfer amount to our merchant. The detail are as follows:-
      </div>

      <!-- Amount Section -->
      <div class="amount-section">
        <span class="amount-label">Amount :</span>
        <span class="amount-value">${formData.amount}</span>
        <span class="amount-label">Bahta.</span>
      </div>

      <!-- Transfer Details Table -->
      <table class="table">
        <tr>
          <th>Transfer from</th>
          <td></td>
        </tr>
        <tr>
          <th>A/C Name</th>
          <td>${formData.fromAccount?.name}</td>
        </tr>
        <tr>
          <th>Type Of A/C</th>
          <td>${formData.fromAccount?.type}</td>
        </tr>
        <tr>
          <th>Deposit Bank</th>
          <td>${formData.fromAccount?.bank}</td>
        </tr>
        <tr>
          <th>Branch</th>
          <td>${formData.fromAccount?.branch}</td>
        </tr>
        <tr>
          <th>A/C No.</th>
          <td>${formData.fromAccount?.accountNo}</td>
        </tr>
      </table>

      <!-- Transaction Details -->
      <div class="transaction-section">
        <div class="transaction-row">
          <div class="transaction-label">Account to be charge</div>
          <div class="transaction-amount">Amount</div>
        </div>
        <div class="transaction-row">
          <div class="transaction-label">Dr. Merchant (Gross)</div>
          <div class="transaction-amount">${formData.transactions?.merchantGross}</div>
        </div>
        <div class="transaction-row">
          <div class="transaction-label">Cr. Merchant (Net)</div>
          <div class="transaction-amount">${formData.transactions?.merchantNet}</div>
        </div>
        <div class="transaction-row">
          <div class="transaction-label">Cr. Discount Earned</div>
          <div class="transaction-amount">${formData.transactions?.discountEarned}</div>
        </div>
        <div class="transaction-row">
          <div class="transaction-label">Cr. Vat 7%</div>
          <div class="transaction-amount">${formData.transactions?.vat}</div>
        </div>
      </div>

      <!-- Description -->
      <div style="font-size: 10px; margin-bottom: 10px;">
        Description : ${formData.description}
      </div>

      <!-- Date Range -->
      <div style="font-size: 10px; margin-bottom: 15px;">
        BBL: ${formData.dateRange?.transfer_dt_bbl} | Other: ${formData.dateRange?.transfer_dt_oth}
      </div>

      <!-- Media Clearing Section -->
      <div class="transaction-section">
        <div style="font-size: 10px; font-weight: bold; margin-bottom: 5px;">Media Clearing</div>
        <div class="transaction-row">
          <div class="transaction-label">Total Amount(Trx.)</div>
          <div class="transaction-amount">${formData.mediaClearing?.totalTransactions}</div>
        </div>
        <div class="transaction-row">
          <div class="transaction-label">Sum Total</div>
          <div class="transaction-amount">${formData.mediaClearing?.sumTotal}</div>
        </div>
      </div>

      <!-- Summary Section -->
      <div class="summary-section">
        <div class="transaction-row">
          <div class="transaction-label bold">Transfer Date</div>
          <div class="transaction-amount">${formData.summary?.transferDate}</div>
        </div>
        <div class="transaction-row">
          <div class="transaction-label">Total Amount(Baht.)</div>
          <div class="transaction-amount">${formData.summary?.totalAmount}</div>
        </div>
        <div class="transaction-row">
          <div class="transaction-label">Less Discount</div>
          <div class="transaction-amount">${formData.summary?.lessDiscount}</div>
        </div>
        <div class="transaction-row">
          <div class="transaction-label">VAT 7%</div>
          <div class="transaction-amount">${formData.summary?.vatAmount}</div>
        </div>
        <div class="transaction-row">
          <div class="transaction-label bold">Net Amount</div>
          <div class="transaction-amount bold">${formData.summary?.netAmount}</div>
        </div>
      </div>

      <!-- Signature Section -->
      <div class="signature-section">
        <div class="signature-box">
          <div class="signature-label">Payment by:</div>
        </div>
        <div class="signature-box">
          <div class="signature-label">Approved by:</div>
        </div>
        <div class="signature-box">
          <div class="signature-label">Checked by:</div>
        </div>
      </div>

      <!-- Transfer Label -->
      <div class="center" style="margin-top: 20px;">
        <div style="font-size: 12px; font-weight: bold;">Transfer</div>
      </div>
    </body>
    </html>
  `;
}

export function setupPaymentRequestFormHandlers() {
  console.log('Setting up Payment Request Form handlers...');

  // Generate Payment Request PDF
  ipcMain.handle('generate-payment-request-pdf', async (_event, data: PaymentRequestData): Promise<PaymentRequestFormResponse> => {
    try {
      console.log('🔧 Generating Payment Request PDF...');

      // For now, we'll generate an HTML file and suggest using puppeteer for PDF generation
      // In a production environment, you might want to use puppeteer or similar library
      const html = generatePaymentRequestHTML(data);

      // Create a temporary HTML file
      const tempDir = path.join(process.cwd(), 'temp');
      await fs.mkdir(tempDir, { recursive: true });
      
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const htmlFilePath = path.join(tempDir, `payment-request-${timestamp}.html`);
      
      await fs.writeFile(htmlFilePath, html, 'utf8');

      console.log('✅ Payment Request HTML generated successfully');
      return {
        success: true,
        message: 'Payment Request HTML generated successfully',
        data: { htmlPath: htmlFilePath, html }
      };

    } catch (error: any) {
      console.error('❌ Error generating Payment Request PDF:', error.message);
      return {
        success: false,
        message: 'Failed to generate Payment Request PDF',
        error: error.message
      };
    }
  });

  // Save Payment Request PDF
  ipcMain.handle('save-payment-request-pdf', async (_event, { data, filePath }: { data: PaymentRequestData; filePath?: string }): Promise<PaymentRequestFormResponse> => {
    try {
      console.log('💾 Saving Payment Request PDF...');

      let targetPath = filePath;
      
      if (!targetPath) {
        // Show save dialog
        const result = await dialog.showSaveDialog({
          title: 'Save Payment Request Form',
          defaultPath: `payment-request-form-${new Date().toISOString().split('T')[0]}.html`,
          filters: [
            { name: 'HTML Files', extensions: ['html'] },
            { name: 'All Files', extensions: ['*'] }
          ]
        });

        if (result.canceled || !result.filePath) {
          return {
            success: false,
            message: 'Save operation was cancelled'
          };
        }

        targetPath = result.filePath;
      }

      // Generate HTML content
      const html = generatePaymentRequestHTML(data);

      // Write to file
      await fs.writeFile(targetPath, html, 'utf8');

      console.log('✅ Payment Request PDF saved successfully');
      return {
        success: true,
        message: 'Payment Request PDF saved successfully',
        data: { filePath: targetPath }
      };

    } catch (error: any) {
      console.error('❌ Error saving Payment Request PDF:', error.message);
      return {
        success: false,
        message: 'Failed to save Payment Request PDF',
        error: error.message
      };
    }
  });

  console.log('✅ Payment Request Form handlers setup complete');
}
