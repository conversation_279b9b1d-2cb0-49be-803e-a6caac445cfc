import { ipc<PERSON><PERSON>, dialog, BrowserWindow, app } from 'electron';
import { customIcon } from '../index';
import fs from 'fs';
import path from 'path';
import { executeQuery, getDbConnection } from '../db';
import { Client } from 'pg';
import { TransactionProcessingService } from '../services/transactionProcessingService';
import { TransactionSummaryReportService } from '../services/transactionSummaryReportService';
import { TransactionAdjustmentService } from '../services/transactionAdjustmentService';
import { PCloudService } from '../services/pcloudService';
import { ensureFolderExists } from '../fileSystemUtils';

// Transaction Payment Form Search Interface Definitions
interface TransactionPaymentFormSearchParams {
  page?: number;
  pageSize?: number;
  search?: string;
  startDate?: string;
  endDate?: string;
  trxNo?: string;
  createdBy?: string;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalRecords: number;
  pageSize: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

interface TransactionPaymentFormSearchResponse {
  success: boolean;
  data?: TransactionPaymentForm[];
  pagination?: PaginationInfo;
  error?: string;
  message?: string;
}

interface TransactionPaymentForm {
  id: number;
  trx_no: string;
  bank_bbl_amount: number;
  bank_oth_amount: number;
  bank_bbl_count: number;
  bank_oth_count: number;
  transfer_fee_bbl: number;
  transfer_fee_oth: number;
  mdr_amount_bbl: number;
  mdr_amount_oth: number;
  vat_amount_bbl: number;
  vat_amount_oth: number;
  sum_amount: number;
  vat_amount: number;
  withhold_amount: number;
  mdr_amount: number;
  net_amount: number;
  approve_dt: string;
  transfer_dt_bbl?: string; // Invoice date for BBL bank
  transfer_dt_oth?: string; // Invoice date for Other banks
  active: boolean;
  create_by: string;
  create_dt: string;
  update_by: string;
  update_dt: string;
}

interface TransactionPaymentFormDetailsResponse {
  success: boolean;
  data?: {
    paymentForm: TransactionPaymentForm;
    transactionDetails: TransactionSummaryDetailItem[];
    summary?: {
      totalTransactions: number;
      totalMerchants: number;
      totalChannels: number;
    };
  };
  error?: string;
}

interface TransactionSummaryDetailItem {
  id: number;
  merchant_vat: string;
  merchant_name: string;
  transaction_date: string;
  channel_type: string;
  transaction_count: number;
  total_amount: number;
  mdr_rate: number;
  mdr_amount: number;
  vat_percentage: number;
  vat_amount: number;
  net_amount: number;
  withholding_tax_rate: number;
  withhold_tax: number;
  transfer_fee: number;
  reimbursement_fee: number;
  service_fee: number;
  final_net_amount: number;
  cup_business_tax_fee: number;
  is_transfer: number;
  bank_ref?: string;
  trx_no?: string;
  create_by: string;
  create_dt: string;
  update_by: string;
  update_dt: string;
}

// Existing interfaces
interface TransactionFilter {
  startDate?: string;
  endDate?: string;
  merchantVat?: string;
  merchantName?: string;
  referenceNo?: string;
  cardNo?: string;
  amount?: number;
  subMchId?: string;
  page?: number;
  limit?: number;
}

interface UploadedFile {
  fileName: string;
  filePath: string;
  size: number;
  uploadTime: Date;
  processed: boolean;
  error?: string;
  isDuplicate?: boolean;
  originalName?: string;
}

interface TransactionPaginationParams {
  page?: number;
  pageSize?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
  startDate?: string;
  endDate?: string;
  merchantVat?: string;
  merchantName?: string;
  referenceNo?: string;
  cardNo?: string;
  amount?: number;
  subMchId?: string;
  status?: string;
  channel?: string;
}

interface TransactionResponse {
  success: boolean;
  message: string;
  data?: any | any[];
  pagination?: {
    currentPage: number;
    totalPages: number;
    totalRecords: number;
    pageSize: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
  error?: string;
}

// Global state for uploaded files
let uploadedFiles: UploadedFile[] = [];

import { getPCloudConfig } from '../config/env';

// Helper function to get consistent upload directory
function getUploadDirectory(): string {
  const userDataPath = app.getPath('userData');
  return path.join(userDataPath, 'temp', 'transactions');
}

// Get pCloud configuration from environment
const getPCloudConfigForTransactions = () => {
  const config = getPCloudConfig();
  return {
    username: config.username,
    password: config.password,
    region: config.region,
    baseFolder: config.baseFolder
  };
};

export function setupTransactionHandlers() {
  console.log('Setting up Transaction handlers...');

  // Upload transaction files with duplicate detection support
  ipcMain.handle('upload-transaction-files', async (_event, files: { name: string; originalName?: string; data: Buffer; isDuplicate?: boolean }[]) => {
    try {
      console.log(`📤 Uploading ${files.length} transaction files`);

      // Use consistent upload directory
      const uploadDir = getUploadDirectory();
      ensureFolderExists(uploadDir);

      console.log(`📁 Upload directory: ${uploadDir}`);

      const uploadedFilesList: UploadedFile[] = [];

      for (const file of files) {
        const filePath = path.join(uploadDir, file.name);
        fs.writeFileSync(filePath, file.data);

        const stats = fs.statSync(filePath);
        const uploadedFile: UploadedFile = {
          fileName: file.name,
          filePath: filePath,
          size: stats.size,
          uploadTime: new Date(),
          processed: false,
          isDuplicate: file.isDuplicate || false,
          originalName: file.originalName || file.name
        };

        uploadedFilesList.push(uploadedFile);

        const duplicateInfo = file.isDuplicate ? ` (renamed from ${file.originalName})` : '';
        console.log(`✅ Uploaded: ${file.name} (${stats.size} bytes)${duplicateInfo}`);
      }

      // Add to global state
      uploadedFiles.push(...uploadedFilesList);

      return {
        success: true,
        message: `Successfully uploaded ${files.length} files`,
        files: uploadedFilesList
      };

    } catch (error) {
      console.error('❌ Error uploading transaction files:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed'
      };
    }
  });

  // Get uploaded files list
  ipcMain.handle('get-uploaded-transaction-files', async () => {
    return {
      success: true,
      files: uploadedFiles
    };
  });

  // Process transaction files (with optional file selection)
  ipcMain.handle('process-transaction-files', async (_event, params: string | { userName: string; selectedFiles?: string[] }) => {
    try {
      // Handle both old string format and new object format for backward compatibility
      let currentUser: string;
      let selectedFileNames: string[] | undefined;

      if (typeof params === 'string') {
        currentUser = params;
        selectedFileNames = undefined; // Process all files
      } else {
        currentUser = params.userName || 'SYSTEM';
        selectedFileNames = params.selectedFiles;
      }

      // Filter files to process based on selection
      const filesToProcess = selectedFileNames
        ? uploadedFiles.filter(file => selectedFileNames.includes(file.fileName) && !file.processed)
        : uploadedFiles.filter(file => !file.processed);

      console.log(`🔄 Processing ${filesToProcess.length} transaction files${selectedFileNames ? ' (selected)' : ''}`);

      // Initialize services

      const processingService = new TransactionProcessingService();
      const summaryReportService = new TransactionSummaryReportService();
      const pcloudConfig = getPCloudConfigForTransactions();
      const pcloudService = new PCloudService(pcloudConfig);

      const results = {
        totalFiles: filesToProcess.length,
        processedFiles: 0,
        failedFiles: 0,
        totalTransactions: 0,
        savedTransactions: 0,
        updatedTransactions: 0,
        duplicateTransactions: 0,
        skippedDuplicates: 0,
        mergedTransactions: 0,
        loggedOnlyDuplicates: 0,
        errors: [] as string[],
        backupResults: [] as any[],
        duplicateDetails: [] as any[]
      };

      for (const file of filesToProcess) {
        if (file.processed) {
          console.log(`⏭️ Skipping already processed file: ${file.fileName}`);
          continue;
        }

        try {
          console.log(`📄 Processing file: ${file.fileName}`);

          // Step 1: Process the file and extract transactions
          console.log(`🔍 DEBUG: About to process file: ${file.fileName} at path: ${file.filePath}`);
          const processingResult = await processingService.processTransactionFile(
            file.filePath,
            file.fileName,
            '', // Will be set after backup
            currentUser
          );

          console.log(`🔍 DEBUG: Processing result for ${file.fileName}:`);
          console.log(`  - Success: ${processingResult.success}`);
          console.log(`  - Total Rows: ${processingResult.totalRows}`);
          console.log(`  - Processed Rows: ${processingResult.processedRows}`);
          console.log(`  - Skipped Rows: ${processingResult.skippedRows}`);
          console.log(`  - Error Rows: ${processingResult.errorRows}`);
          console.log(`  - Transactions: ${processingResult.transactions.length}`);
          console.log(`  - Errors: ${processingResult.errors.join(', ')}`);

          if (processingResult.transactions.length > 0) {
            console.log(`🔍 DEBUG: First transaction sample:`);
            const firstTxn = processingResult.transactions[0];
            console.log(`  - ID: ${firstTxn.transaction_id}`);
            console.log(`  - Amount: ${firstTxn.transaction_amount}`);
            console.log(`  - Time: ${firstTxn.transaction_time}`);
            console.log(`  - Status: ${firstTxn.transaction_trade_status}`);
          }

          if (!processingResult.success) {
            throw new Error(`Processing failed: ${processingResult.errors.join(', ')}`);
          }

          results.totalTransactions += processingResult.processedRows;

          // Step 2: Backup to pCloud with date-based folder
          let backupPath = '';
          try {
            console.log(`☁️ Backing up to pCloud: ${file.fileName}`);
            const uploadResult = await pcloudService.uploadFileWithDateFolder(
              file.filePath,
              pcloudConfig.baseFolder,
              new Date()
            );

            if (uploadResult.success) {
              backupPath = uploadResult.remotePath || '';
              console.log(`✅ pCloud backup successful: ${backupPath}`);
              results.backupResults.push({
                fileName: file.fileName,
                success: true,
                remotePath: backupPath
              });
            } else {
              console.warn(`⚠️ pCloud backup failed: ${uploadResult.error}`);
              results.backupResults.push({
                fileName: file.fileName,
                success: false,
                error: uploadResult.error
              });
            }
          } catch (backupError) {
            console.warn(`⚠️ pCloud backup error for ${file.fileName}:`, backupError);
            results.backupResults.push({
              fileName: file.fileName,
              success: false,
              error: backupError instanceof Error ? backupError.message : 'Backup failed'
            });
          }

          // Update backup path in transactions
          processingResult.transactions.forEach(t => {
            t.transaction_file_name_backup = backupPath;
          });

          // Step 3: Save ALL transactions to database (including duplicates)
          const duplicateHandlingOptions = {
            strategy: 'INSERT_ALL' as const, // Insert all records including duplicates
            logDuplicates: true,
            updateFields: []
          };

          const saveResult = await processingService.saveTransactions(
            processingResult.transactions,
            duplicateHandlingOptions
          );

          results.savedTransactions += saveResult.savedCount;
          results.duplicateTransactions += saveResult.duplicateCount;

          // Enhanced duplicate tracking (when available)
          if ('updatedCount' in saveResult) {
            results.updatedTransactions += (saveResult as any).updatedCount;
          }
          if ('duplicateDetails' in saveResult) {
            results.duplicateDetails.push(...(saveResult as any).duplicateDetails);
          }
          
          if (saveResult.errors.length > 0) {
            results.errors.push(...saveResult.errors);
          }

          // Mark file as processed
          file.processed = true;
          results.processedFiles++;

          // Enhanced logging for this file
          console.log(`✅ File processed successfully: ${file.fileName}`);
          console.log(`   📊 File Stats: ${processingResult.processedRows} transactions processed, ${saveResult.savedCount} records inserted (all records saved)`);

          // Log duplicate details if any (all records are always inserted)
          if (saveResult.duplicateCount > 0) {
            console.log(`   📝 Duplicate Analysis for ${file.fileName} (all records inserted):`);
            if ('duplicateDetails' in saveResult) {
              const duplicateDetails = (saveResult as any).duplicateDetails;
              duplicateDetails.forEach((dup: any, index: number) => {
                console.log(`     ${index + 1}. Transaction ID: ${dup.transaction_id}`);
                console.log(`        Previous Record: ฿${dup.original_amount.toFixed(2)} (${dup.original_file})`);
                console.log(`        Current Record: ฿${dup.duplicate_amount.toFixed(2)} (${dup.duplicate_file})`);
                console.log(`        Amount Difference: ฿${(dup.duplicate_amount - dup.original_amount).toFixed(2)}`);
                console.log(`        Result: All records inserted - no data skipped`);
                console.log(`        Source: ${dup.original_file === dup.duplicate_file ? 'Same file' : 'Different files'}`);
              });
            } else {
              // Fallback for basic duplicate logging
              // console.log(`     ${saveResult.duplicateCount} duplicate transaction(s) found - all records inserted`);
            }
          }

        } catch (error) {
          results.failedFiles++;
          const errorMsg = `File ${file.fileName}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          results.errors.push(errorMsg);
          file.error = errorMsg;
          console.error(`❌ Error processing file ${file.fileName}:`, error);
        }
      }

      // Enhanced completion logging
      console.log(`🎉 Processing complete: ${results.processedFiles}/${results.totalFiles} files processed`);
      console.log(`📊 Transaction Summary - All Records Inserted:`);
      console.log(`   💾 Total Records Inserted: ${results.savedTransactions} transactions`);
      console.log(`   📝 Duplicates Found: ${results.duplicateTransactions} transactions (all inserted, none skipped)`);

      if (results.updatedTransactions > 0) {
        console.log(`   🔄 Updated: ${results.updatedTransactions} transactions`);
      }

      // Detailed duplicate analysis
      if (results.duplicateTransactions > 0) {
        console.log(`📋 Duplicate Analysis - All Records Inserted:`);

        const sameFileDuplicates = results.duplicateDetails.filter((d: any) => d.original_file === d.duplicate_file).length;
        const crossFileDuplicates = results.duplicateDetails.filter((d: any) => d.original_file !== d.duplicate_file).length;

        if (sameFileDuplicates > 0) {
          console.log(`   📄 Same File Duplicates: ${sameFileDuplicates} (multiple records from same file - all inserted)`);
        }
        if (crossFileDuplicates > 0) {
          console.log(`   📁 Cross File Duplicates: ${crossFileDuplicates} (same transaction in multiple files - all inserted)`);
        }

        // Amount difference analysis
        const exactMatches = results.duplicateDetails.filter((d: any) => Math.abs(d.duplicate_amount - d.original_amount) < 0.01).length;
        const amountDifferences = results.duplicateDetails.filter((d: any) => Math.abs(d.duplicate_amount - d.original_amount) >= 0.01).length;

        if (exactMatches > 0) {
          console.log(`   💰 Exact Amount Matches: ${exactMatches} (identical amounts - all records preserved)`);
        }
        if (amountDifferences > 0) {
          console.log(`   💸 Amount Differences: ${amountDifferences} (different amounts for same ID - all variations saved)`);
        }

        // Show top duplicate transaction IDs
        const duplicateIds = results.duplicateDetails.map((d: any) => d.transaction_id).slice(0, 5);
        if (duplicateIds.length > 0) {
          console.log(`   🔍 Sample Duplicate IDs: ${duplicateIds.join(', ')}${duplicateIds.length < results.duplicateDetails.length ? '...' : ''}`);
        }

        console.log(`   ✅ Result: All ${results.savedTransactions} records inserted - no data lost or skipped`);
      }

      // Enhanced workflow: Generate and save transaction summary report if files were successfully processed
      if (results.processedFiles > 0 && results.savedTransactions > 0) {
        try {
          const batchId = `BATCH_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
          const processedFileNames = filesToProcess
            .filter(file => file.processed)
            .map(file => file.fileName);

          console.log(`📊 Enhanced workflow: Generating transaction summary report for batch: ${batchId}`);
          console.log(`📊 Processing ${processedFileNames.length} files with ${results.savedTransactions} transactions`);

          // Use the enhanced workflow that groups by merchant_id from transaction_e_pos
          const reportResult = await summaryReportService.generateSummaryFromUploadedTransactions(
            batchId,
            processedFileNames,
            currentUser
          );

          if (reportResult.success) {
            console.log(`✅ Enhanced workflow: Transaction summary details saved successfully`);
            console.log(`📊 Generated ${reportResult.summaryDetails?.length || 0} merchant summary records`);

            // Add report info to results
            (results as any).summaryReportBatchId = batchId;
            (results as any).merchantSummaryCount = reportResult.summaryDetails?.length || 0;

            // Log summary details for verification
            if (reportResult.summaryDetails && reportResult.summaryDetails.length > 0) {
              console.log(`📊 Merchant summaries generated:`);
              reportResult.summaryDetails.forEach((summary, index) => {
                console.log(`  ${index + 1}. ${summary.merchantName} (${summary.merchantVat}): ${summary.transactionCount} transactions, ${summary.totalAmount} THB`);
              });
            }
          } else {
            console.error(`❌ Failed to save enhanced transaction summary report: ${reportResult.error}`);
            // Don't fail the entire process if report generation fails
            results.errors.push(`Enhanced summary report generation failed: ${reportResult.error}`);
          }
        } catch (error) {
          console.error('❌ Error in enhanced transaction summary workflow:', error);
          results.errors.push(`Enhanced summary report error: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      return {
        success: results.processedFiles > 0,
        ...results
      };

    } catch (error) {
      console.error('❌ Error processing transaction files:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Processing failed'
      };
    }
  });

  // Clear uploaded files
  ipcMain.handle('clear-uploaded-transaction-files', async () => {
    try {
      // Clean up temporary files
      for (const file of uploadedFiles) {
        try {
          if (fs.existsSync(file.filePath)) {
            fs.unlinkSync(file.filePath);
          }
        } catch (error) {
          console.warn(`⚠️ Could not delete temp file: ${file.filePath}`);
        }
      }

      uploadedFiles = [];
      
      return { success: true, message: 'Uploaded files cleared' };
    } catch (error) {
      console.error('❌ Error clearing uploaded files:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Clear failed'
      };
    }
  });

  // Get transactions with filtering
  ipcMain.handle('get-transactions', async (_event, filter: TransactionFilter = {}) => {
    try {
      const { page = 1, limit = 50 } = filter;
      const offset = (page - 1) * limit;

      // Build WHERE clause
      const conditions: string[] = [];
      const params: any[] = [];
      let paramIndex = 1;

      if (filter.startDate) {
        conditions.push(`transaction_time >= $${paramIndex}`);
        params.push(filter.startDate);
        paramIndex++;
      }

      if (filter.endDate) {
        conditions.push(`transaction_time <= $${paramIndex}`);
        params.push(filter.endDate + ' 23:59:59');
        paramIndex++;
      }

      if (filter.merchantVat) {
        conditions.push(`transaction_merchant_vat ILIKE $${paramIndex}`);
        params.push(`%${filter.merchantVat}%`);
        paramIndex++;
      }

      if (filter.merchantName) {
        conditions.push(`transaction_merchant_name ILIKE $${paramIndex}`);
        params.push(`%${filter.merchantName}%`);
        paramIndex++;
      }

      if (filter.referenceNo) {
        conditions.push(`reference_no ILIKE $${paramIndex}`);
        params.push(`%${filter.referenceNo}%`);
        paramIndex++;
      }

      if (filter.cardNo) {
        conditions.push(`transaction_card_no ILIKE $${paramIndex}`);
        params.push(`%${filter.cardNo}%`);
        paramIndex++;
      }

      if (filter.amount) {
        conditions.push(`transaction_amount = $${paramIndex}`);
        params.push(filter.amount);
        paramIndex++;
      }

      if (filter.subMchId) {
        conditions.push(`transaction_sub_mch_id ILIKE $${paramIndex}`);
        params.push(`%${filter.subMchId}%`);
        paramIndex++;
      }

      const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

      // Get total count
      const countQuery = `SELECT COUNT(*) as total FROM transaction_e_pos ${whereClause}`;
      const countResult = await executeQuery(countQuery, params);
      const total = parseInt(countResult.rows[0].total);

      // Get transactions
      const transactionsQuery = `
        SELECT * FROM transaction_e_pos
        ${whereClause}
        ORDER BY transaction_time DESC
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;
      params.push(limit, offset);

      const transactionsResult = await executeQuery(transactionsQuery, params);

      return {
        success: true,
        transactions: transactionsResult.rows,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };

    } catch (error) {
      console.error('❌ Error getting transactions:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Query failed'
      };
    }
  });

  // Get transaction statistics
  ipcMain.handle('get-transaction-statistics', async () => {
    try {
      const statsQuery = `
        SELECT 
          COUNT(*) as total_transactions,
          COUNT(DISTINCT transaction_merchant_id) as unique_merchants,
          SUM(CASE WHEN transaction_amount > 0 THEN transaction_amount ELSE 0 END) as total_sales,
          SUM(CASE WHEN transaction_amount < 0 THEN ABS(transaction_amount) ELSE 0 END) as total_refunds,
          COUNT(CASE WHEN transaction_trade_status = 'success' THEN 1 END) as successful_transactions,
          COUNT(CASE WHEN transaction_trade_status = 'refund' THEN 1 END) as refund_transactions,
          COUNT(DISTINCT transaction_channel_type) as unique_channels
        FROM transaction_e_pos
      `;

      const result = await executeQuery(statsQuery);
      const stats = result.rows[0];

      return {
        success: true,
        statistics: {
          totalTransactions: parseInt(stats.total_transactions),
          uniqueMerchants: parseInt(stats.unique_merchants),
          totalSales: parseFloat(stats.total_sales) || 0,
          totalRefunds: parseFloat(stats.total_refunds) || 0,
          successfulTransactions: parseInt(stats.successful_transactions),
          refundTransactions: parseInt(stats.refund_transactions),
          uniqueChannels: parseInt(stats.unique_channels)
        }
      };

    } catch (error) {
      console.error('❌ Error getting transaction statistics:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Statistics query failed'
      };
    }
  });

  // Get transactions with pagination (similar to bank master)
  ipcMain.handle('get-transactions-paginated', async (_event, params: TransactionPaginationParams = {}): Promise<TransactionResponse> => {
    try {
      const {
        page = 1,
        pageSize = 20,
        search = '',
        sortBy = 'transaction_time',
        sortOrder = 'DESC',
        startDate,
        endDate,
        merchantVat,
        merchantName,
        referenceNo,
        cardNo,
        amount,
        subMchId,
        status,
        channel
      } = params;

      console.log(`📊 Getting transactions - Page ${page}, Size ${pageSize}, Search: "${search}"`);
      console.log(`🔍 Filter params:`, {
        startDate, endDate, merchantVat, merchantName, referenceNo,
        cardNo, amount, subMchId, status, channel
      });

      // Validate pagination parameters
      const validPage = Math.max(1, page);
      const validPageSize = Math.min(Math.max(1, pageSize), 100); // Max 100 per page
      const offset = (validPage - 1) * validPageSize;

      // Validate sort parameters
      const validSortColumns = [
        'id', 'transaction_time', 'transaction_id', 'transaction_merchant_name',
        'transaction_merchant_vat', 'transaction_sub_mch_id', 'transaction_amount', 
        'transaction_trade_status', 'transaction_channel_type', 'reference_no'
      ];
      const validSortBy = validSortColumns.includes(sortBy) ? sortBy : 'transaction_time';
      const validSortOrder = sortOrder === 'ASC' ? 'ASC' : 'DESC';

      // Build WHERE clause
      const conditions: string[] = [];
      const searchParams: any[] = [];
      let paramIndex = 1;

      // Search functionality
      if (search.trim()) {
        conditions.push(`(
          transaction_id ILIKE $${paramIndex} OR
          transaction_merchant_name ILIKE $${paramIndex} OR
          transaction_merchant_vat ILIKE $${paramIndex} OR
          transaction_sub_mch_id ILIKE $${paramIndex}
        )`);
        searchParams.push(`%${search.trim()}%`);
        paramIndex++;
      }

      // Date range filter
      if (startDate) {
        conditions.push(`transaction_time >= $${paramIndex}`);
        searchParams.push(startDate);
        paramIndex++;
      }

      if (endDate) {
        conditions.push(`transaction_time <= $${paramIndex}`);
        searchParams.push(endDate + ' 23:59:59');
        paramIndex++;
      }

      // Merchant VAT filter
      if (merchantVat?.trim()) {
        conditions.push(`transaction_merchant_vat ILIKE $${paramIndex}`);
        searchParams.push(`%${merchantVat.trim()}%`);
        paramIndex++;
      }

      // Status filter
      if (status?.trim()) {
        conditions.push(`transaction_trade_status = $${paramIndex}`);
        searchParams.push(status.trim().toLowerCase());
        paramIndex++;
      }

      // Channel filter
      if (channel?.trim()) {
        conditions.push(`transaction_channel_type = $${paramIndex}`);
        searchParams.push(channel.trim());
        paramIndex++;
      }

      // Merchant Name filter
      if (merchantName?.trim()) {
        conditions.push(`transaction_merchant_name ILIKE $${paramIndex}`);
        searchParams.push(`%${merchantName.trim()}%`);
        paramIndex++;
      }

      // Reference Number filter
      if (referenceNo?.trim()) {
        conditions.push(`reference_no ILIKE $${paramIndex}`);
        searchParams.push(`%${referenceNo.trim()}%`);
        paramIndex++;
      }

      // Card Number filter
      if (cardNo?.trim()) {
        conditions.push(`transaction_card_no ILIKE $${paramIndex}`);
        searchParams.push(`%${cardNo.trim()}%`);
        paramIndex++;
      }

      // Amount filter (exact match)
      if (amount !== undefined && amount !== null && !isNaN(amount)) {
        conditions.push(`transaction_amount = $${paramIndex}`);
        searchParams.push(amount);
        paramIndex++;
      }

      // Sub Merchant ID (TID) filter
      if (subMchId?.trim()) {
        conditions.push(`transaction_sub_mch_id ILIKE $${paramIndex}`);
        searchParams.push(`%${subMchId.trim()}%`);
        paramIndex++;
      }

      const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

      // Get total count
      const countQuery = `SELECT COUNT(*) as total FROM transaction_e_pos ${whereClause}`;
      const countResult = await executeQuery(countQuery, searchParams);
      const totalRecords = parseInt(countResult.rows[0].total);
      const totalPages = Math.ceil(totalRecords / validPageSize);
      const hasNextPage = validPage < totalPages;
      const hasPreviousPage = validPage > 1;

      // Get transactions
      const limitParam = paramIndex;
      const offsetParam = paramIndex + 1;

      const dataQuery = `
        SELECT id, reference_no, transaction_id, transaction_out_id, transaction_card_no,
               transaction_merchant_id, transaction_merchant_name, transaction_merchant_vat,
               transaction_time, transaction_amount, transaction_refund_id, transaction_refund_out_id,
               transaction_mch_id, transaction_sub_mch_id, transaction_trade_type, transaction_trade_status,
               transaction_bank_type, transaction_fee_type, transaction_coupon_amount,
               transaction_file_name, transaction_file_name_backup, transaction_channel_type,
               create_by, create_dt, update_by, update_dt
        FROM transaction_e_pos
        ${whereClause}
        ORDER BY ${validSortBy} ${validSortOrder}
        LIMIT $${limitParam} OFFSET $${offsetParam}
      `;

      const dataParams = [...searchParams, validPageSize, offset];

      console.log(`🔍 Query conditions: ${conditions.length} filters applied`);
      console.log(`📝 Search params:`, searchParams);
      console.log(`📄 Data params:`, dataParams);
      console.log(`🗃️ WHERE clause: ${whereClause}`);

      const result = await executeQuery(dataQuery, dataParams);

      console.log(`✅ Found ${result.rows.length} transactions (Page ${validPage}/${totalPages}, Total: ${totalRecords})`);

      return {
        success: true,
        message: `Found ${result.rows.length} transactions on page ${validPage}`,
        data: result.rows,
        pagination: {
          currentPage: validPage,
          totalPages,
          totalRecords,
          pageSize: validPageSize,
          hasNextPage,
          hasPreviousPage
        }
      };

    } catch (error: any) {
      console.error('❌ Error fetching transactions:', error.message);
      return {
        success: false,
        message: 'Failed to fetch transactions',
        error: error.message
      };
    }
  });

  // Insert test transaction data for testing filters
  // ipcMain.handle('insert-test-transaction-data', async (): Promise<TransactionResponse> => {
  //   try {
  //     console.log('🧪 Inserting test transaction data...');
      
  //     const testData = [
  //       {
  //         reference_no: 'REF001',
  //         transaction_id: 'TXN_2025_001',
  //         transaction_out_id: 'OUT_001',
  //         transaction_card_no: '1234****5678',
  //         transaction_merchant_id: 'MCH_001',
  //         transaction_merchant_name: 'Test Merchant A',
  //         transaction_merchant_vat: '*************',
  //         transaction_time: '2025-07-16 10:00:00',
  //         transaction_amount: 1000.00,
  //         transaction_mch_id: '**********',
  //         transaction_sub_mch_id: '*********',
  //         transaction_trade_type: 'MICROPAY',
  //         transaction_trade_status: 'SUCCESS',
  //         transaction_bank_type: 'ABC_DEBIT',
  //         transaction_fee_type: 'THB',
  //         transaction_coupon_amount: 0.00,
  //         transaction_file_name: 'test_file_1.csv',
  //         transaction_channel_type: 'WeChat',
  //         create_by: 'TEST_USER'
  //       },
  //       {
  //         reference_no: 'REF002',
  //         transaction_id: 'TXN_2025_002',
  //         transaction_out_id: 'OUT_002',
  //         transaction_card_no: '9876****4321',
  //         transaction_merchant_id: 'MCH_002',
  //         transaction_merchant_name: 'Test Merchant B',
  //         transaction_merchant_vat: '*************',
  //         transaction_time: '2025-07-16 11:00:00',
  //         transaction_amount: 2500.50,
  //         transaction_mch_id: '**********',
  //         transaction_sub_mch_id: '*********',
  //         transaction_trade_type: 'MICROPAY',
  //         transaction_trade_status: 'SUCCESS',
  //         transaction_bank_type: 'CMB_CREDIT',
  //         transaction_fee_type: 'THB',
  //         transaction_coupon_amount: 100.00,
  //         transaction_file_name: 'test_file_2.csv',
  //         transaction_channel_type: 'ALIPAY',
  //         create_by: 'TEST_USER'
  //       },
  //       {
  //         reference_no: 'REF003',
  //         transaction_id: 'TXN_2025_003',
  //         transaction_out_id: 'OUT_003',
  //         transaction_card_no: '5555****1111',
  //         transaction_merchant_id: 'MCH_001',
  //         transaction_merchant_name: 'Test Merchant A',
  //         transaction_merchant_vat: '*************',
  //         transaction_time: '2025-07-16 12:00:00',
  //         transaction_amount: 750.25,
  //         transaction_mch_id: '**********',
  //         transaction_sub_mch_id: '*********',
  //         transaction_trade_type: 'MICROPAY',
  //         transaction_trade_status: 'REFUND',
  //         transaction_bank_type: 'BOC_DEBIT',
  //         transaction_fee_type: 'THB',
  //         transaction_coupon_amount: 0.00,
  //         transaction_file_name: 'test_file_3.csv',
  //         transaction_channel_type: 'WeChat',
  //         create_by: 'TEST_USER'
  //       },
  //       {
  //         reference_no: 'REF004',
  //         transaction_id: 'TXN_2025_004',
  //         transaction_out_id: 'OUT_004',
  //         transaction_card_no: '4444****8888',
  //         transaction_merchant_id: 'MCH_003',
  //         transaction_merchant_name: 'Test Merchant C',
  //         transaction_merchant_vat: '*************',
  //         transaction_time: '2025-07-16 13:00:00',
  //         transaction_amount: 3200.75,
  //         transaction_mch_id: '**********',
  //         transaction_sub_mch_id: '*********',
  //         transaction_trade_type: 'MICROPAY',
  //         transaction_trade_status: 'SUCCESS',
  //         transaction_bank_type: 'SPDB_CREDIT',
  //         transaction_fee_type: 'THB',
  //         transaction_coupon_amount: 50.00,
  //         transaction_file_name: 'test_file_4.csv',
  //         transaction_channel_type: 'UNIPAY',
  //         create_by: 'TEST_USER'
  //       },
  //       {
  //         reference_no: 'REF005',
  //         transaction_id: 'TXN_2025_005',
  //         transaction_out_id: 'OUT_005',
  //         transaction_card_no: '7777****3333',
  //         transaction_merchant_id: 'MCH_002',
  //         transaction_merchant_name: 'Test Merchant B',
  //         transaction_merchant_vat: '*************',
  //         transaction_time: '2025-07-16 14:00:00',
  //         transaction_amount: 500.00,
  //         transaction_mch_id: '**********',
  //         transaction_sub_mch_id: '*********',
  //         transaction_trade_type: 'MICROPAY',
  //         transaction_trade_status: 'PENDING',
  //         transaction_bank_type: 'OTHERS',
  //         transaction_fee_type: 'THB',
  //         transaction_coupon_amount: 0.00,
  //         transaction_file_name: 'test_file_5.csv',
  //         transaction_channel_type: 'WeChat',
  //         create_by: 'TEST_USER'
  //       }
  //     ];
      
  //     // First, delete existing test data
  //     await executeQuery('DELETE FROM transaction_e_pos WHERE create_by = $1', ['TEST_USER']);
      
  //     // Insert test data
  //     for (const data of testData) {
  //       const insertQuery = `
  //         INSERT INTO transaction_e_pos (
  //           reference_no, transaction_id, transaction_out_id, transaction_card_no,
  //           transaction_merchant_id, transaction_merchant_name, transaction_merchant_vat,
  //           transaction_time, transaction_amount, transaction_mch_id, transaction_sub_mch_id,
  //           transaction_trade_type, transaction_trade_status, transaction_bank_type,
  //           transaction_fee_type, transaction_coupon_amount, transaction_file_name,
  //           transaction_channel_type, create_by
  //         ) VALUES (
  //           $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19
  //         )
  //       `;
        
  //       const params = [
  //         data.reference_no, data.transaction_id, data.transaction_out_id, data.transaction_card_no,
  //         data.transaction_merchant_id, data.transaction_merchant_name, data.transaction_merchant_vat,
  //         data.transaction_time, data.transaction_amount, data.transaction_mch_id, data.transaction_sub_mch_id,
  //         data.transaction_trade_type, data.transaction_trade_status, data.transaction_bank_type,
  //         data.transaction_fee_type, data.transaction_coupon_amount, data.transaction_file_name,
  //         data.transaction_channel_type, data.create_by
  //       ];
        
  //       await executeQuery(insertQuery, params);
  //     }
      
  //     console.log(`✅ Inserted ${testData.length} test transaction records`);
      
  //     return {
  //       success: true,
  //       message: `Successfully inserted ${testData.length} test transaction records`,
  //       data: testData.length
  //     };
      
  //   } catch (error: any) {
  //     console.error('❌ Error inserting test data:', error.message);
  //     return {
  //       success: false,
  //       message: 'Failed to insert test data',
  //       error: error.message
  //     };
  //   }
  // });

  // Get enhanced transactions with financial calculations for reports
  ipcMain.handle('get-enhanced-transactions-for-report', async (_event, params: TransactionPaginationParams): Promise<any> => {
    try {
      console.log('📊 Fetching enhanced transactions for report with params:', params);

      const {
        page = 1,
        pageSize = 50,
        search = '',
        sortBy = 'transaction_time',
        sortOrder = 'DESC',
        startDate,
        endDate,
        merchantVat,
        merchantName,
        referenceNo,
        cardNo,
        amount,
        subMchId,
        status,
        channel
      } = params;

      // Build the enhanced query with joins to get financial data and bank information
      // Note: Using CASE to safely cast transaction_merchant_id to integer, handling null/empty values
      let query = `
        SELECT
          t.*,
          COALESCE(m.withholding_tax, 0) as withholding_tax,
          COALESCE(m.transfer_fee, 0) as transfer_fee,
          COALESCE(mw.wechat_rate, 0) as wechat_rate,
          COALESCE(ns.vat_percentage, 0) as vat_percentage,
          b.bank_code,
          b.bank_name_th,
          b.bank_name_en
        FROM transaction_e_pos t
        LEFT JOIN merchant m ON (
          t.transaction_merchant_id = m.merchant_ref
        )
        LEFT JOIN merchant_wechat mw ON m.merchant_id = mw.merchant_id AND mw.active = true
        LEFT JOIN merchant_bank mb ON m.merchant_id = mb.merchant_id AND mb.active = true
        LEFT JOIN tmst_bank b ON mb.bank_id = b.bank_id AND b.active = true
        LEFT JOIN network_service ns ON ns.active = true
        WHERE 1=1
      `;

      const queryParams: any[] = [];
      let paramIndex = 1;

      // Add filters
      if (search) {
        query += ` AND (
          t.transaction_merchant_name ILIKE $${paramIndex} OR
          t.transaction_merchant_vat ILIKE $${paramIndex} OR
          t.reference_no ILIKE $${paramIndex} OR
          t.transaction_id ILIKE $${paramIndex} OR
          t.transaction_card_no ILIKE $${paramIndex}
        )`;
        queryParams.push(`%${search}%`);
        paramIndex++;
      }

      if (startDate) {
        query += ` AND t.transaction_time >= $${paramIndex}`;
        queryParams.push(startDate);
        paramIndex++;
      }

      if (endDate) {
        query += ` AND t.transaction_time <= $${paramIndex}`;
        queryParams.push(endDate);
        paramIndex++;
      }

      if (merchantVat) {
        query += ` AND t.transaction_merchant_vat ILIKE $${paramIndex}`;
        queryParams.push(`%${merchantVat}%`);
        paramIndex++;
      }

      if (merchantName) {
        query += ` AND t.transaction_merchant_name ILIKE $${paramIndex}`;
        queryParams.push(`%${merchantName}%`);
        paramIndex++;
      }

      if (referenceNo) {
        query += ` AND t.reference_no ILIKE $${paramIndex}`;
        queryParams.push(`%${referenceNo}%`);
        paramIndex++;
      }

      if (cardNo) {
        query += ` AND t.transaction_card_no ILIKE $${paramIndex}`;
        queryParams.push(`%${cardNo}%`);
        paramIndex++;
      }

      if (amount !== undefined && amount !== null && !isNaN(amount)) {
        query += ` AND t.transaction_amount = $${paramIndex}`;
        queryParams.push(amount);
        paramIndex++;
      }

      if (subMchId) {
        query += ` AND t.transaction_sub_mch_id ILIKE $${paramIndex}`;
        queryParams.push(`%${subMchId}%`);
        paramIndex++;
      }

      if (status) {
        query += ` AND t.transaction_trade_status ILIKE $${paramIndex}`;
        queryParams.push(`%${status}%`);
        paramIndex++;
      }

      if (channel) {
        query += ` AND t.transaction_channel_type ILIKE $${paramIndex}`;
        queryParams.push(`%${channel}%`);
        paramIndex++;
      }

      // Add sorting
      const validSortColumns = [
        'transaction_time', 'transaction_amount', 'transaction_merchant_name',
        'transaction_merchant_vat', 'reference_no', 'transaction_id',
        'transaction_card_no', 'transaction_trade_status', 'transaction_channel_type'
      ];

      const sortColumn = validSortColumns.includes(sortBy) ? `t.${sortBy}` : 't.transaction_time';
      const sortDirection = sortOrder === 'ASC' ? 'ASC' : 'DESC';
      query += ` ORDER BY ${sortColumn} ${sortDirection}`;

      // Add pagination
      const offset = (page - 1) * pageSize;
      query += ` LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
      queryParams.push(pageSize, offset);

      console.log('🔍 Enhanced query:', query);
      console.log('🔍 Query params:', queryParams);

      const result = await executeQuery(query, queryParams);

      console.log(`✅ Found ${result.rows.length} enhanced transactions for report`);

      return {
        success: true,
        data: result.rows,
        message: `Found ${result.rows.length} enhanced transactions`
      };

    } catch (error: any) {
      console.error('❌ Error fetching enhanced transactions for report:', error.message);
      return {
        success: false,
        message: 'Failed to fetch enhanced transactions for report',
        error: error.message
      };
    }
  });

  // Show save dialog for Excel export
  ipcMain.handle('show-save-dialog', async (_event, options: { defaultPath?: string; filters?: any[] }) => {
    try {
      const mainWindow = BrowserWindow.getAllWindows()[0];

      const dialogOptions: any = {
        title: 'Save Excel File',
        defaultPath: options.defaultPath || 'transactions_export.xlsx',
        filters: options.filters || [
          { name: 'Excel Files', extensions: ['xlsx'] },
          { name: 'All Files', extensions: ['*'] }
        ]
      };

      // Add custom icon if available
      if (customIcon) {
        dialogOptions.icon = customIcon;
      }

      let result;
      if (mainWindow) {
        result = await dialog.showSaveDialog(mainWindow, dialogOptions);
      } else {
        result = await dialog.showSaveDialog(dialogOptions);
      }

      return {
        success: true,
        canceled: result.canceled,
        filePath: result.filePath
      };
    } catch (error: any) {
      console.error('❌ Error showing save dialog:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  });

  // Save Excel file to specified path
  ipcMain.handle('save-excel-file', async (_event, filePath: string, buffer: Buffer) => {
    try {
      fs.writeFileSync(filePath, buffer);
      console.log(`✅ Excel file saved to: ${filePath}`);

      return {
        success: true,
        filePath: filePath,
        message: 'File saved successfully'
      };
    } catch (error: any) {
      console.error('❌ Error saving Excel file:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  });

  // Get transaction summary details (replaces get-transaction-summary-reports)
  ipcMain.handle('get-transaction-summary-details', async (_event, filters: {
    startDate?: string;
    endDate?: string;
    merchantVat?: string;
    channelType?: string;
    isTransfer?: number;
    page?: number;
    pageSize?: number;
  } = {}) => {
    try {
      const { startDate, endDate, merchantVat, channelType, isTransfer, page = 1, pageSize = 20 } = filters;
      const offset = (page - 1) * pageSize;

      console.log('🔍 get-transaction-summary-details called with filters:', {
        startDate, endDate, merchantVat, channelType, isTransfer, page, pageSize
      });

      let whereConditions = [];
      let queryParams: any[] = [];
      let paramIndex = 1;

      if (startDate) {
        whereConditions.push(`d.transaction_date >= $${paramIndex}`);
        queryParams.push(startDate);
        paramIndex++;
      }

      if (endDate) {
        whereConditions.push(`d.transaction_date <= $${paramIndex}`);
        queryParams.push(endDate);
        paramIndex++;
      }

      if (merchantVat) {
        whereConditions.push(`d.merchant_vat = $${paramIndex}`);
        queryParams.push(merchantVat);
        paramIndex++;
      }

      if (channelType) {
        whereConditions.push(`d.channel_type = $${paramIndex}`);
        queryParams.push(channelType);
        paramIndex++;
      }

      if (isTransfer !== undefined) {
        whereConditions.push(`d.is_transfer = $${paramIndex}`);
        queryParams.push(isTransfer);
        paramIndex++;
      }

      const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

      console.log('📝 SQL WHERE clause:', whereClause);
      console.log('📝 Query parameters:', queryParams);

      // Get total count
      const countResult = await executeQuery(
        `SELECT COUNT(*) as total FROM transaction_summary_report_detail d
         LEFT JOIN merchant m ON d.merchant_vat = m.merchant_vat
         LEFT JOIN merchant_bank mb ON m.merchant_id = mb.merchant_id AND mb.active = true
         LEFT JOIN tmst_bank b ON mb.bank_id = b.bank_id AND b.active = true
         ${whereClause}`,
        queryParams
      );
      const totalRecords = parseInt(countResult.rows[0].total);

      console.log(`📊 Total records found: ${totalRecords}`);

      // Get paginated results with bank information
      const dataResult = await executeQuery(`
        SELECT
          d.id, d.merchant_vat, d.merchant_name, d.transaction_date, d.channel_type,
          d.transaction_count, d.total_amount, d.mdr_rate, d.mdr_amount, d.vat_percentage,
          d.vat_amount, d.net_amount, d.withholding_tax_rate, d.withhold_tax, d.transfer_fee,
          d.reimbursement_fee, d.service_fee, d.final_net_amount, d.cup_business_tax_fee,
          d.is_transfer, d.create_by, d.create_dt, d.update_by, d.update_dt,
          mb.bank_id, mb.bank_account_no, b.bank_code, b.bank_name_th, b.bank_name_en, m.merchant_ref,
          d.trade_status
        FROM transaction_summary_report_detail d
        LEFT JOIN merchant m ON d.merchant_vat = m.merchant_vat
        LEFT JOIN merchant_bank mb ON m.merchant_id = mb.merchant_id AND mb.active = true
        LEFT JOIN tmst_bank b ON mb.bank_id = b.bank_id AND b.active = true
        ${whereClause}
        ORDER BY d.transaction_date DESC, d.merchant_vat
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `, [...queryParams, pageSize, offset]);

      console.log(`📋 Retrieved ${dataResult.rows.length} records for page ${page}`);

      // Log sample data for debugging
      if (dataResult.rows.length > 0) {
        console.log('📋 Sample record:', {
          id: dataResult.rows[0].id,
          merchant_vat: dataResult.rows[0].merchant_vat,
          transaction_date: dataResult.rows[0].transaction_date,
          is_transfer: dataResult.rows[0].is_transfer,
          final_net_amount: dataResult.rows[0].final_net_amount
        });
      }

      return {
        success: true,
        data: dataResult.rows,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(totalRecords / pageSize),
          totalRecords,
          pageSize,
          hasNextPage: page < Math.ceil(totalRecords / pageSize),
          hasPreviousPage: page > 1
        }
      };

    } catch (error) {
      console.error('❌ Error fetching transaction summary details:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch details'
      };
    }
  });

  // Get transaction summary aggregates (replaces main report data)
  ipcMain.handle('get-transaction-summary-aggregates', async (_event, filters: {
    startDate?: string;
    endDate?: string;
    merchantVat?: string;
    channelType?: string;
  } = {}) => {
    try {
      const { startDate, endDate, merchantVat, channelType } = filters;

      let whereConditions = [];
      let queryParams: any[] = [];
      let paramIndex = 1;

      if (startDate) {
        whereConditions.push(`transaction_date >= $${paramIndex}`);
        queryParams.push(startDate);
        paramIndex++;
      }

      if (endDate) {
        whereConditions.push(`transaction_date <= $${paramIndex}`);
        queryParams.push(endDate);
        paramIndex++;
      }

      if (merchantVat) {
        whereConditions.push(`merchant_vat = $${paramIndex}`);
        queryParams.push(merchantVat);
        paramIndex++;
      }

      if (channelType) {
        whereConditions.push(`channel_type = $${paramIndex}`);
        queryParams.push(channelType);
        paramIndex++;
      }

      const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

      // Get aggregated totals
      const aggregateResult = await executeQuery(`
        SELECT
          SUM(CASE WHEN is_transfer = 1 THEN final_net_amount ELSE 0 END) as total_transferred,
          SUM(CASE WHEN is_transfer = 0 THEN final_net_amount ELSE 0 END) as total_pending,
          SUM(final_net_amount) as total_amount,
          SUM(total_amount) as gross_total_amount,
          SUM(mdr_amount) as total_mdr_amount,
          SUM(vat_amount) as total_vat_amount,
          SUM(withhold_tax) as total_withhold_tax,
          SUM(transfer_fee) as total_transfer_fee,
          COUNT(*) as record_count,
          COUNT(DISTINCT merchant_vat) as merchant_count,
          COUNT(DISTINCT channel_type) as channel_count
        FROM transaction_summary_report_detail
        ${whereClause}
      `, queryParams);

      // Get channel breakdown
      const channelResult = await executeQuery(`
        SELECT
          channel_type,
          COUNT(*) as record_count,
          SUM(final_net_amount) as total_amount,
          SUM(CASE WHEN is_transfer = 1 THEN final_net_amount ELSE 0 END) as transferred_amount,
          SUM(CASE WHEN is_transfer = 0 THEN final_net_amount ELSE 0 END) as pending_amount
        FROM transaction_summary_report_detail
        ${whereClause}
        GROUP BY channel_type
        ORDER BY total_amount DESC
      `, queryParams);

      const aggregates = aggregateResult.rows[0];
      const channelBreakdown = channelResult.rows;

      return {
        success: true,
        data: {
          totalTransferred: parseFloat(aggregates.total_transferred || 0),
          totalPending: parseFloat(aggregates.total_pending || 0),
          totalAmount: parseFloat(aggregates.total_amount || 0),
          grossTotalAmount: parseFloat(aggregates.gross_total_amount || 0),
          totalMdrAmount: parseFloat(aggregates.total_mdr_amount || 0),
          totalVatAmount: parseFloat(aggregates.total_vat_amount || 0),
          totalWithholdTax: parseFloat(aggregates.total_withhold_tax || 0),
          totalTransferFee: parseFloat(aggregates.total_transfer_fee || 0),
          recordCount: parseInt(aggregates.record_count || 0),
          merchantCount: parseInt(aggregates.merchant_count || 0),
          channelCount: parseInt(aggregates.channel_count || 0),
          channelBreakdown
        }
      };

    } catch (error) {
      console.error('❌ Error fetching transaction summary aggregates:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch aggregates'
      };
    }
  });

  // Get transaction summary report details (deprecated - use get-transaction-summary-details instead)
  ipcMain.handle('get-transaction-summary-report-details', async (_event, reportId: number) => {
    try {
      console.warn('⚠️ get-transaction-summary-report-details is deprecated. Use get-transaction-summary-details instead.');

      // For backward compatibility, return detail data without main report dependency
      const detailResult = await executeQuery(`
        SELECT * FROM transaction_summary_report_detail
        WHERE id = $1
        ORDER BY merchant_vat
      `, [reportId]);

      return {
        success: true,
        data: {
          report: null, // No longer using main report table
          details: detailResult.rows
        }
      };

    } catch (error) {
      console.error('❌ Error fetching transaction summary report details:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch report details'
      };
    }
  });

  // Approve transaction summary (mark all as transferred for a date range)
  ipcMain.handle('approve-transaction-summary', async (_event, filters: {
    startDate?: string;
    endDate?: string;
    merchantVat?: string;
    channelType?: string;
  }, approvedBy: string) => {
    try {
      let whereConditions = [];
      let queryParams: any[] = [];
      let paramIndex = 1;

      if (filters.startDate) {
        whereConditions.push(`transaction_date >= $${paramIndex}`);
        queryParams.push(filters.startDate);
        paramIndex++;
      }

      if (filters.endDate) {
        whereConditions.push(`transaction_date <= $${paramIndex}`);
        queryParams.push(filters.endDate);
        paramIndex++;
      }

      if (filters.merchantVat) {
        whereConditions.push(`merchant_vat = $${paramIndex}`);
        queryParams.push(filters.merchantVat);
        paramIndex++;
      }

      if (filters.channelType) {
        whereConditions.push(`channel_type = $${paramIndex}`);
        queryParams.push(filters.channelType);
        paramIndex++;
      }

      // Only approve records that are not yet transferred
      whereConditions.push(`is_transfer = 0`);

      const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

      const result = await executeQuery(`
        UPDATE transaction_summary_report_detail
        SET is_transfer = 1, update_by = $${paramIndex}, update_dt = CURRENT_TIMESTAMP
        ${whereClause}
      `, [...queryParams, approvedBy]);

      return {
        success: true,
        message: `${result.rowCount || 0} transaction summaries approved and marked as transferred`
      };

    } catch (error) {
      console.error('❌ Error approving transaction summaries:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to approve summaries'
      };
    }
  });

  // Get transaction summary details for today (filtered by create_dt)
  ipcMain.handle('get-transaction-summary-today', async (_event, filters: {
    merchantVat?: string;
    channelType?: string;
    isTransfer?: number;
    page?: number;
    pageSize?: number;
  } = {}) => {
    try {
      const { merchantVat, channelType, isTransfer, page = 1, pageSize = 20 } = filters;
      const offset = (page - 1) * pageSize;

      console.log('🔍 get-transaction-summary-today called with filters:', {
        merchantVat, channelType, isTransfer, page, pageSize
      });

      let whereConditions = ['(DATE(d.create_dt) = CURRENT_DATE OR DATE(d.update_dt) = CURRENT_DATE)']; // Filter for today's records (created or updated today)
      let queryParams: any[] = [];
      let paramIndex = 1;

      if (merchantVat) {
        whereConditions.push(`d.merchant_vat = $${paramIndex}`);
        queryParams.push(merchantVat);
        paramIndex++;
      }

      if (channelType) {
        whereConditions.push(`d.channel_type = $${paramIndex}`);
        queryParams.push(channelType);
        paramIndex++;
      }

      if (isTransfer !== undefined) {
        whereConditions.push(`d.is_transfer = $${paramIndex}`);
        queryParams.push(isTransfer);
        paramIndex++;
      }

      const whereClause = `WHERE ${whereConditions.join(' AND ')}`;

      console.log('📝 SQL WHERE clause for today (created or updated):', whereClause);
      console.log('📝 Query parameters:', queryParams);

      // Get total count for today (created or updated today)
      const countResult = await executeQuery(
        `SELECT COUNT(*) as total FROM transaction_summary_report_detail d
         LEFT JOIN merchant m ON d.merchant_vat = m.merchant_vat
         LEFT JOIN merchant_bank mb ON m.merchant_id = mb.merchant_id AND mb.active = true
         LEFT JOIN tmst_bank b ON mb.bank_id = b.bank_id AND b.active = true
         ${whereClause}`,
        queryParams
      );
      const totalRecords = parseInt(countResult.rows[0].total);

      console.log(`📊 Total records found for today (created or updated): ${totalRecords}`);

      // Get paginated results for today (created or updated today) with bank information
      const dataResult = await executeQuery(`
        SELECT
          d.id, d.merchant_vat, d.merchant_name, d.transaction_date, d.channel_type,
          d.transaction_count, d.total_amount, d.mdr_rate, d.mdr_amount, d.vat_percentage,
          d.vat_amount, d.net_amount, d.withholding_tax_rate, d.withhold_tax, d.transfer_fee,
          d.reimbursement_fee, d.service_fee, d.final_net_amount, d.cup_business_tax_fee,
          d.is_transfer, d.create_by, d.create_dt, d.update_by, d.update_dt,
          mb.bank_id, mb.bank_account_no, b.bank_code, b.bank_name_th, b.bank_name_en, m.merchant_ref,
          d.trx_no,d.trade_status
        FROM transaction_summary_report_detail d
        LEFT JOIN merchant m ON d.merchant_vat = m.merchant_vat
        LEFT JOIN merchant_bank mb ON m.merchant_id = mb.merchant_id AND mb.active = true
        LEFT JOIN tmst_bank b ON mb.bank_id = b.bank_id AND b.active = true
        ${whereClause}
        ORDER BY d.trx_no desc, d.merchant_vat
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `, [...queryParams, pageSize, offset]);

      console.log(`📋 Retrieved ${dataResult.rows.length} records for today's transactions - created or updated (page ${page})`);

      // Log sample data for debugging
      if (dataResult.rows.length > 0) {
        console.log('📋 Sample today record (created or updated):', {
          id: dataResult.rows[0].id,
          merchant_vat: dataResult.rows[0].merchant_vat,
          transaction_date: dataResult.rows[0].transaction_date,
          create_dt: dataResult.rows[0].create_dt,
          is_transfer: dataResult.rows[0].is_transfer,
          final_net_amount: dataResult.rows[0].final_net_amount
        });
      }

      return {
        success: true,
        data: dataResult.rows,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(totalRecords / pageSize),
          totalRecords,
          pageSize,
          hasNextPage: page < Math.ceil(totalRecords / pageSize),
          hasPreviousPage: page > 1
        },
        todayDate: new Date().toISOString().split('T')[0] // Include today's date for reference (includes created or updated today)
      };

    } catch (error) {
      console.error('❌ Error fetching today\'s transaction summary details (created or updated):', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch today\'s transaction details (created or updated)'
      };
    }
  });

  // Update transfer status for transaction summary report detail
  ipcMain.handle('update-transfer-status', async (_event, detailId: number, isTransfer: number, updatedBy: string) => {
    try {
      if (![0, 1].includes(isTransfer)) {
        return {
          success: false,
          error: 'Invalid transfer status. Must be 0 (not transferred) or 1 (transferred)'
        };
      }

      await executeQuery(`
        UPDATE transaction_summary_report_detail
        SET is_transfer = $1, update_by = $2, update_dt = CURRENT_TIMESTAMP
        WHERE id = $3
      `, [isTransfer, updatedBy, detailId]);

      return {
        success: true,
        message: `Transfer status updated to ${isTransfer === 1 ? 'transferred' : 'not transferred'}`
      };

    } catch (error) {
      console.error('❌ Error updating transfer status:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update transfer status'
      };
    }
  });

  // Get transaction summary report details with transfer status
  ipcMain.handle('get-transfer-status-report', async (_event, filters: {
    startDate?: string;
    endDate?: string;
    isTransfer?: number;
    page?: number;
    pageSize?: number;
  } = {}) => {
    try {
      const { startDate, endDate, isTransfer, page = 1, pageSize = 50 } = filters;
      const offset = (page - 1) * pageSize;

      let whereConditions = [];
      let queryParams: any[] = [];
      let paramIndex = 1;

      if (startDate) {
        whereConditions.push(`d.transaction_date >= $${paramIndex}`);
        queryParams.push(startDate);
        paramIndex++;
      }

      if (endDate) {
        whereConditions.push(`d.transaction_date <= $${paramIndex}`);
        queryParams.push(endDate);
        paramIndex++;
      }

      if (isTransfer !== undefined) {
        whereConditions.push(`d.is_transfer = $${paramIndex}`);
        queryParams.push(isTransfer);
        paramIndex++;
      }

      const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

      // Get total count
      const countResult = await executeQuery(`
        SELECT COUNT(*) as total
        FROM transaction_summary_report_detail d
        ${whereClause}
      `, queryParams);
      const totalRecords = parseInt(countResult.rows[0].total);

      // Get paginated results
      const dataResult = await executeQuery(`
        SELECT
          d.id, d.merchant_vat, d.merchant_name, d.transaction_date, d.channel_type,
          d.transaction_count, d.total_amount, d.transfer_fee, d.final_net_amount,
          d.is_transfer, d.create_by, d.create_dt, d.update_by, d.update_dt,
          d.mdr_amount, d.vat_amount, d.withhold_tax, d.reimbursement_fee, d.service_fee
        FROM transaction_summary_report_detail d
        ${whereClause}
        ORDER BY d.transaction_date DESC, d.merchant_vat
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `, [...queryParams, pageSize, offset]);

      return {
        success: true,
        data: dataResult.rows,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(totalRecords / pageSize),
          totalRecords,
          pageSize,
          hasNextPage: page < Math.ceil(totalRecords / pageSize),
          hasPreviousPage: page > 1
        }
      };

    } catch (error) {
      console.error('❌ Error fetching transfer status report:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch transfer status report'
      };
    }
  });

  // Bulk update transfer status for multiple records
  ipcMain.handle('bulk-update-transfer-status', async (_event, params: {
    detailIds: number[];
    isTransfer: number;
    updatedBy: string;
  }) => {
    try {
      const { detailIds, isTransfer, updatedBy } = params;

      if (![0, 1].includes(isTransfer)) {
        return {
          success: false,
          error: 'Invalid transfer status. Must be 0 (not transferred) or 1 (transferred)'
        };
      }

      if (!detailIds || detailIds.length === 0) {
        return {
          success: false,
          error: 'No detail IDs provided for update'
        };
      }

      // Create placeholders for the IN clause
      const placeholders = detailIds.map((_, index) => `$${index + 3}`).join(',');

      const result = await executeQuery(`
        UPDATE transaction_summary_report_detail
        SET is_transfer = $1, update_by = $2, update_dt = CURRENT_TIMESTAMP
        WHERE id IN (${placeholders})
      `, [isTransfer, updatedBy, ...detailIds]);

      return {
        success: true,
        message: `Updated ${result.rowCount} records to ${isTransfer === 1 ? 'transferred' : 'not transferred'}`,
        updatedCount: result.rowCount
      };

    } catch (error) {
      console.error('❌ Error bulk updating transfer status:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to bulk update transfer status'
      };
    }
  });

  // Debug handler to check transaction_summary_report_detail table
  ipcMain.handle('debug-transaction-summary-table', async () => {
    try {
      console.log('🔍 Debugging transaction_summary_report_detail table...');

      // Check if table exists
      const tableExistsResult = await executeQuery(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables
          WHERE table_schema = 'public'
          AND table_name = 'transaction_summary_report_detail'
        );
      `);
      console.log('📊 Table exists:', tableExistsResult.rows[0].exists);

      if (!tableExistsResult.rows[0].exists) {
        return {
          success: false,
          error: 'Table transaction_summary_report_detail does not exist'
        };
      }

      // Get table structure
      const structureResult = await executeQuery(`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns
        WHERE table_name = 'transaction_summary_report_detail'
        ORDER BY ordinal_position;
      `);
      console.log('📊 Table structure:', structureResult.rows);

      // Get record counts
      const countResult = await executeQuery(`
        SELECT
          COUNT(*) as total_records,
          COUNT(CASE WHEN is_transfer = 0 THEN 1 END) as pending_records,
          COUNT(CASE WHEN is_transfer = 1 THEN 1 END) as transferred_records
        FROM transaction_summary_report_detail;
      `);
      console.log('📊 Record counts:', countResult.rows[0]);

      // Get sample data
      const sampleResult = await executeQuery(`
        SELECT * FROM transaction_summary_report_detail
        ORDER BY create_dt DESC
        LIMIT 5;
      `);
      console.log('📊 Sample data:', sampleResult.rows);

      return {
        success: true,
        data: {
          tableExists: tableExistsResult.rows[0].exists,
          structure: structureResult.rows,
          counts: countResult.rows[0],
          sampleData: sampleResult.rows
        }
      };

    } catch (error) {
      console.error('❌ Error debugging table:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // Handler to get the next transaction number from transaction_payment_form table
  ipcMain.handle('get-next-trx-number', async () => {
    try {
      console.log('🔢 Getting next transaction number from transaction_payment_form table...');
      
      // Query to get the next transaction number using YYMM-XXXX format
      const query = `
        WITH latest AS (
          SELECT trx_no
          FROM transaction_payment_form
          WHERE trx_no LIKE TO_CHAR(NOW(), 'YYMM') || '-%'
          ORDER BY trx_no DESC
          LIMIT 1
        )
        SELECT 
          TO_CHAR(NOW(), 'YYMM') || '-' || 
          RIGHT(
            '0000' || COALESCE(SUBSTRING((SELECT trx_no FROM latest) FROM 6 FOR 4)::INT + 1, 1)::TEXT,
            4
          ) AS next_trx_no
      `;

      const result = await executeQuery(query);
      
      if (result.rows && result.rows.length > 0) {
        const nextTrxNo = result.rows[0].next_trx_no;
        console.log(`✅ Next transaction number: ${nextTrxNo}`);
        
        return {
          success: true,
          data: {
            next_trx_no: nextTrxNo
          }
        };
      } else {
        // If no records found, start with current month format YYMM-0001
        const currentMonth = new Date().toISOString().slice(2, 7).replace('-', ''); // YYMM format
        const defaultTrxNo = `${currentMonth}-0001`;
        console.log(`✅ No existing records, starting with: ${defaultTrxNo}`);
        
        return {
          success: true,
          data: {
            next_trx_no: defaultTrxNo
          }
        };
      }

    } catch (error) {
      console.error('❌ Error getting next transaction number:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  });

  // Enhanced workflow: Approve selected transactions with running number integration
  ipcMain.handle('approve-selected-transactions', async (_event, params: {
    detailIds: number[];
    updatedBy: string;
    runningDate?: string;
    transferDtBbl?: string | null;
    transferDtOth?: string | null;
  }) => {
    try {
      const { detailIds, updatedBy, runningDate, transferDtBbl, transferDtOth } = params;

      if (!detailIds || detailIds.length === 0) {
        return {
          success: false,
          error: 'No transactions selected for approval'
        };
      }

      if (!updatedBy) {
        return {
          success: false,
          error: 'User information required for approval'
        };
      }

      console.log(`🚀 Enhanced workflow: Approving ${detailIds.length} transactions with running number integration...`);

      // Step 1: Get the next transaction number
      console.log('📋 Step 1: Getting next transaction number...');
      const today = runningDate || new Date().toISOString().split('T')[0];
      const year = today.substring(2, 4); // YY format
      const month = today.substring(5, 7); // MM format
      const prefix = `${year}${month}-`;

      const nextNumberQuery = `
        SELECT 
          COALESCE(
            CAST(
              RIGHT(
                SPLIT_PART(
                  MAX(trx_no), 
                  '-', 
                  2
                )::TEXT,
                4
              ) AS INTEGER
            ) + 1,
            1
          ) as next_number
        FROM transaction_payment_form
        WHERE trx_no LIKE $1
      `;

      const nextNumberResult = await executeQuery(nextNumberQuery, [`${prefix}%`]);
      const nextNumber = nextNumberResult.rows[0]?.next_number || 1;
      const nextTrxNo = `${prefix}${String(nextNumber).padStart(4, '0')}`;

      console.log(`📝 Generated transaction number: ${nextTrxNo}`);

      // Step 2: Get transaction details for the payment form
      console.log('📊 Step 2: Retrieving transaction details...');
      const placeholders = detailIds.map((_, index) => `$${index + 1}`).join(',');
      
      const transactionDetailsQuery = `
        SELECT 
          id,
          merchant_vat,
          merchant_name,
          transaction_date,
          total_amount,
          net_amount,
          vat_amount,
          withhold_tax,
          mdr_amount,
          transfer_fee,
          is_transfer,
          bank_ref,
          transaction_count
        FROM transaction_summary_report_detail
        WHERE id IN (${placeholders})
        AND is_transfer = 0
      `;

      const transactionDetails = await executeQuery(transactionDetailsQuery, detailIds);
      
      if (transactionDetails.rows.length === 0) {
        return {
          success: false,
          error: 'No valid pending transactions found for approval'
        };
      }

      console.log(`📊 Found ${transactionDetails.rows.length} valid pending transactions`);

      // Step 3: Calculate payment form totals separated by bank_ref
      console.log('💰 Step 3: Calculating payment form totals with bank separation...');
      const transactions = transactionDetails.rows;
      
      // Separate calculations for BBL and other banks
      const bankSeparatedTotals = transactions.reduce((acc, txn) => {
        const bankRef = txn.bank_ref;
        const isBBL = bankRef === 'BBL' || bankRef === 'bbl';
        const amount = Number(txn.total_amount || 0);
        const transactionCount = Number(txn.transaction_count || 0);
        
        return {
          // Total amounts (all banks combined)
          sum_amount: acc.sum_amount + Number(txn.total_amount || 0),
          net_amount: acc.net_amount + Number(txn.net_amount || 0),
          vat_amount: acc.vat_amount + Number(txn.vat_amount || 0),
          withhold_amount: acc.withhold_amount + Number(txn.withhold_tax || 0),
          mdr_amount: acc.mdr_amount + Number(txn.mdr_amount || 0),
          
          // BBL bank amounts
          bank_bbl_amount: acc.bank_bbl_amount + (isBBL ? amount : 0),
          bank_bbl_count: acc.bank_bbl_count + (isBBL ? transactionCount : 0),
          transfer_fee_bbl: acc.transfer_fee_bbl + (isBBL ? Number(txn.transfer_fee || 0) : 0),
          
          // Other bank amounts
          bank_oth_amount: acc.bank_oth_amount + (!isBBL ? amount : 0),
          bank_oth_count: acc.bank_oth_count + (!isBBL ? transactionCount : 0),
          transfer_fee_oth: acc.transfer_fee_oth + (!isBBL ? Number(txn.transfer_fee || 0) : 0),
          mdr_amount_bbl: acc.mdr_amount_bbl + (isBBL ? Number(txn.mdr_amount || 0) : 0),
          mdr_amount_oth: acc.mdr_amount_oth + (!isBBL ? Number(txn.mdr_amount || 0) : 0),
          vat_amount_bbl: acc.vat_amount_bbl + (isBBL ? Number(txn.vat_amount || 0) : 0),
          vat_amount_oth: acc.vat_amount_oth + (!isBBL ? Number(txn.vat_amount || 0) : 0)
        };
      }, {
        sum_amount: 0,
        net_amount: 0,
        vat_amount: 0,
        withhold_amount: 0,
        mdr_amount: 0,
        bank_bbl_amount: 0,
        bank_oth_amount: 0,
        bank_bbl_count: 0,
        bank_oth_count: 0,
        transfer_fee_bbl: 0,
        transfer_fee_oth: 0,
        mdr_amount_bbl: 0,
        mdr_amount_oth: 0,
        vat_amount_bbl: 0,
        vat_amount_oth: 0,
      });

      console.log(`💰 Bank separation: BBL=${bankSeparatedTotals.bank_bbl_amount}, Other=${bankSeparatedTotals.bank_oth_amount}`);

      // Step 4: Create payment form entry with bank separation
      console.log('📝 Step 4: Creating payment form entry with bank amounts...');
      const createPaymentFormQuery = `
        INSERT INTO transaction_payment_form (
          trx_no,
          bank_bbl_amount,
          bank_oth_amount,
          bank_bbl_count,
          bank_oth_count,
          transfer_fee_bbl,
          transfer_fee_oth,
          mdr_amount_bbl,
          mdr_amount_oth,
          vat_amount_bbl,
          vat_amount_oth,
          sum_amount,
          vat_amount,
          withhold_amount,
          mdr_amount,
          net_amount,
          approve_dt,
          transfer_dt_bbl,
          transfer_dt_oth,
          active,
          create_by,
          create_dt,
          update_by,
          update_dt
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, CURRENT_TIMESTAMP, $22, CURRENT_TIMESTAMP)
        RETURNING id, trx_no
      `;

      const paymentFormResult = await executeQuery(createPaymentFormQuery, [
        nextTrxNo,
        bankSeparatedTotals.bank_bbl_amount,
        bankSeparatedTotals.bank_oth_amount,
        bankSeparatedTotals.bank_bbl_count,
        bankSeparatedTotals.bank_oth_count,
        bankSeparatedTotals.transfer_fee_bbl,
        bankSeparatedTotals.transfer_fee_oth,
        bankSeparatedTotals.mdr_amount_bbl,
        bankSeparatedTotals.mdr_amount_oth,
        bankSeparatedTotals.vat_amount_bbl,
        bankSeparatedTotals.vat_amount_oth,
        bankSeparatedTotals.sum_amount,
        bankSeparatedTotals.vat_amount,
        bankSeparatedTotals.withhold_amount,
        bankSeparatedTotals.mdr_amount,
        bankSeparatedTotals.net_amount,
        today, // approve_dt - matches the running date
        transferDtBbl, // transfer_dt_bbl - BBL invoice date
        transferDtOth, // transfer_dt_oth - Other invoice date
        true, // active
        updatedBy,
        updatedBy // update_by
      ]);

      const paymentFormId = paymentFormResult.rows[0]?.id;
      console.log(`✅ Created payment form with ID: ${paymentFormId}, Trx No: ${nextTrxNo}`);

      // Step 5: Update transaction details with trx_no and transfer status
      console.log('🔄 Step 5: Updating transaction details...');
      console.log(`📊 Debug: Updating ${detailIds.length} transactions:`, detailIds);
      console.log(`📊 Debug: Parameters - trx_no: ${nextTrxNo}, updatedBy: ${updatedBy}`);
      
      // Process each transaction individually to avoid parameter binding issues
      let updatedCount = 0;
      for (const detailId of detailIds) {
        try {
          const individualUpdateQuery = `
            UPDATE transaction_summary_report_detail
            SET 
              trx_no = $1,
              is_transfer = 1,
              update_by = $2,
              update_dt = CURRENT_TIMESTAMP
            WHERE id = $3
            AND is_transfer = 0
          `;
          
          const individualResult = await executeQuery(individualUpdateQuery, [nextTrxNo, updatedBy, detailId]);
          updatedCount += individualResult.rowCount || 0;
          console.log(`✅ Updated transaction ID ${detailId}, rows affected: ${individualResult.rowCount}`);
        } catch (individualError) {
          console.error(`❌ Failed to update transaction ID ${detailId}:`, individualError);
          throw individualError;
        }
      }
      
      console.log(`✅ Updated ${updatedCount} transaction details in total`);

      // Step 6: Return comprehensive result
      const result = {
        success: true,
        message: `Successfully approved ${updatedCount} transactions with transaction number ${nextTrxNo}`,
        data: {
          trx_no: nextTrxNo,
          payment_form_id: paymentFormId,
          approved_count: updatedCount,
          total_amount: bankSeparatedTotals.sum_amount,
          net_amount: bankSeparatedTotals.net_amount,
          vat_amount: bankSeparatedTotals.vat_amount,
          withhold_amount: bankSeparatedTotals.withhold_amount,
          mdr_amount: bankSeparatedTotals.mdr_amount,
          bank_bbl_amount: bankSeparatedTotals.bank_bbl_amount,
          bank_oth_amount: bankSeparatedTotals.bank_oth_amount,
          bank_bbl_count: bankSeparatedTotals.bank_bbl_count,
          bank_oth_count: bankSeparatedTotals.bank_oth_count,
          transfer_fee_bbl: bankSeparatedTotals.transfer_fee_bbl,
          transfer_fee_oth: bankSeparatedTotals.transfer_fee_oth,
          mdr_amount_bbl: bankSeparatedTotals.mdr_amount_bbl,
          mdr_amount_oth: bankSeparatedTotals.mdr_amount_oth,
          vat_amount_bbl: bankSeparatedTotals.vat_amount_bbl,
          vat_amount_oth: bankSeparatedTotals.vat_amount_oth,
          created_by: updatedBy,
          created_at: new Date().toISOString()
        }
      };

      console.log('🎉 Enhanced workflow completed successfully:', result.data);
      return result;

    } catch (error) {
      console.error('❌ Error in enhanced approval workflow:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to approve selected transactions'
      };
    }
  });

  // Test handler for enhanced workflow calculations (without database insertion)
  ipcMain.handle('test-enhanced-workflow-calculations', async (_event, params: {
    detailIds: number[];
    updatedBy: string;
    runningDate?: string;
    mockTransactions?: any[];
  }) => {
    try {
      const { detailIds, updatedBy, runningDate, mockTransactions } = params;

      console.log('🧪 Testing enhanced workflow calculations (no database operations)');
      console.log(`📊 Testing with ${detailIds.length} detail IDs`);

      // Use mock data if provided, otherwise use real data from database
      let transactionDetails;
      
      if (mockTransactions && mockTransactions.length > 0) {
        console.log('📋 Using provided mock transaction data');
        transactionDetails = { rows: mockTransactions.filter(txn => detailIds.includes(txn.id)) };
      } else {
        console.log('📋 Fetching real transaction data for testing');
        const placeholders = detailIds.map((_, index) => `$${index + 1}`).join(',');
        
        const transactionDetailsQuery = `
          SELECT 
            id,
            merchant_vat,
            merchant_name,
            transaction_date,
            total_amount,
            final_net_amount,
            vat_amount,
            withhold_tax,
            mdr_amount,
            transfer_fee,
            is_transfer,
            bank_ref,
            transaction_count,
            net_amount
          FROM transaction_summary_report_detail
          WHERE id IN (${placeholders})
        `;

        transactionDetails = await executeQuery(transactionDetailsQuery, detailIds);
      }

      // Step 1: Generate transaction number (test calculation)
      const today = runningDate || new Date().toISOString().split('T')[0];
      const year = today.substring(2, 4); // YY format
      const month = today.substring(5, 7); // MM format
      const prefix = `${year}${month}-`;

      // For testing, simulate getting next number
      let nextNumber = 1;
      if (!mockTransactions) {
        const nextNumberQuery = `
          SELECT 
            COALESCE(
              CAST(
                RIGHT(
                  SPLIT_PART(
                    MAX(trx_no), 
                    '-', 
                    2
                  )::TEXT,
                  4
                ) AS INTEGER
              ) + 1,
              1
            ) as next_number
          FROM transaction_payment_form
          WHERE trx_no LIKE $1
        `;

        const nextNumberResult = await executeQuery(nextNumberQuery, [`${prefix}%`]);
        nextNumber = nextNumberResult.rows[0]?.next_number || 1;
      }

      const nextTrxNo = `${prefix}${String(nextNumber).padStart(4, '0')}`;

      // Step 2: Filter valid pending transactions
      const validTransactions = transactionDetails.rows.filter(txn => txn.is_transfer === 0);

      if (validTransactions.length === 0) {
        return {
          success: false,
          error: 'No valid pending transactions found for approval',
          testData: {
            total_requested: detailIds.length,
            found_in_db: transactionDetails.rows.length,
            valid_pending: 0
          }
        };
      }

      // Step 3: Calculate payment form totals with bank separation
      const totals = validTransactions.reduce((acc, txn) => {
        const bankRef = txn.bank_ref;
        const isBBL = bankRef === 'BBL' || bankRef === 'bbl';
        const amount = Number(txn.net_amount || 0);
        const transactionCount = Number(txn.transaction_count || 0);
        
        return {
          sum_amount: acc.sum_amount + Number(txn.net_amount || 0),
          net_amount: acc.net_amount + Number(txn.net_amount || 0),
          vat_amount: acc.vat_amount + Number(txn.vat_amount || 0),
          withhold_amount: acc.withhold_amount + Number(txn.withhold_tax || 0),
          mdr_amount: acc.mdr_amount + Number(txn.mdr_amount || 0),
          bank_bbl_amount: acc.bank_bbl_amount + (isBBL ? amount : 0),
          bank_bbl_count: acc.bank_bbl_count + (isBBL ? transactionCount : 0),
          transfer_fee_bbl: acc.transfer_fee_bbl + (isBBL ? Number(txn.transfer_fee || 0) : 0),
          bank_oth_amount: acc.bank_oth_amount + (!isBBL ? amount : 0),
          bank_oth_count: acc.bank_oth_count + (!isBBL ? transactionCount : 0),
          transfer_fee_oth: acc.transfer_fee_oth + (!isBBL ? Number(txn.transfer_fee || 0) : 0),
          mdr_amount_bbl: acc.mdr_amount_bbl + (isBBL ? Number(txn.mdr_amount || 0) : 0),
          mdr_amount_oth: acc.mdr_amount_oth + (!isBBL ? Number(txn.mdr_amount || 0) : 0),
          vat_amount_bbl: acc.vat_amount_bbl + (isBBL ? Number(txn.vat_amount || 0) : 0),
          vat_amount_oth: acc.vat_amount_oth + (!isBBL ? Number(txn.vat_amount || 0) : 0)
        };
      }, {
        sum_amount: 0,
        net_amount: 0,
        vat_amount: 0,
        withhold_amount: 0,
        mdr_amount: 0,
        bank_bbl_amount: 0,
        bank_oth_amount: 0,
        bank_bbl_count: 0,
        bank_oth_count: 0,
        transfer_fee_bbl: 0,
        transfer_fee_oth: 0,
        mdr_amount_bbl: 0,
        mdr_amount_oth: 0,
        vat_amount_bbl: 0,
        vat_amount_oth: 0,
      });

      // Step 4: Generate merchant breakdown with bank information
      const merchantBreakdown = validTransactions.reduce((acc, txn) => {
        const key = txn.merchant_vat;
        if (!acc[key]) {
          acc[key] = {
            merchant_name: txn.merchant_name,
            transaction_count: 0,
            total_amount: 0,
            net_amount: 0,
            vat_amount: 0,
            withhold_tax: 0,
            mdr_amount: 0,
            bank_refs: new Set()
          };
        }
        
        acc[key].transaction_count++;
        acc[key].total_amount += Number(txn.total_amount || 0);
        acc[key].net_amount += Number(txn.net_amount || 0);
        acc[key].vat_amount += Number(txn.vat_amount || 0);
        acc[key].withhold_tax += Number(txn.withhold_tax || 0);
        acc[key].mdr_amount += Number(txn.mdr_amount || 0);
        if (txn.bank_ref) {
          acc[key].bank_refs.add(txn.bank_ref);
        }
        
        return acc;
      }, {});

      // Step 5: Validation checks
      const validationResults = [];
      
      if (totals.sum_amount <= 0) {
        validationResults.push({ status: 'error', message: 'Sum amount must be positive' });
      } else {
        validationResults.push({ status: 'success', message: 'Sum amount is positive' });
      }
      
      if (totals.net_amount <= 0) {
        validationResults.push({ status: 'error', message: 'Net amount must be positive' });
      } else {
        validationResults.push({ status: 'success', message: 'Net amount is positive' });
      }
      
      // if (totals.net_amount > totals.sum_amount) {
      //   validationResults.push({ status: 'warning', message: 'Net amount exceeds sum amount (unusual)' });
      // } else {
      //   validationResults.push({ status: 'success', message: 'Net amount is within valid range' });
      // }
      
      const trxNoPattern = /^\d{4}-\d{4}$/;
      if (!trxNoPattern.test(nextTrxNo)) {
        validationResults.push({ status: 'error', message: 'Transaction number format is invalid' });
      } else {
        validationResults.push({ status: 'success', message: 'Transaction number format is valid' });
      }

      const hasErrors = validationResults.some(v => v.status === 'error');

      // Step 6: Generate SQL preview (for reference)
      const sqlPreview = {
        paymentFormInsert: {
          query: `INSERT INTO transaction_payment_form (trx_no, bank_bbl_amount, bank_oth_amount, bank_bbl_count, bank_oth_count, transfer_fee_bbl, transfer_fee_oth, mdr_amount_bbl, mdr_amount_oth, vat_amount_bbl, vat_amount_oth, sum_amount, vat_amount, withhold_amount, mdr_amount, net_amount, approve_dt, active, create_by, create_dt, update_by, update_dt) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, CURRENT_TIMESTAMP)`,
          values: [nextTrxNo, totals.bank_bbl_amount, totals.bank_oth_amount, totals.bank_bbl_count, totals.bank_oth_count, totals.transfer_fee_bbl, totals.transfer_fee_oth, totals.mdr_amount_bbl, totals.mdr_amount_oth, totals.vat_amount_bbl, totals.vat_amount_oth, totals.sum_amount, totals.vat_amount, totals.withhold_amount, totals.mdr_amount, totals.net_amount, today, true, updatedBy, updatedBy]
        },
        transactionUpdates: validTransactions.map(txn => ({
          query: `UPDATE transaction_summary_report_detail SET trx_no = ?, is_transfer = 1, update_by = ?, update_dt = CURRENT_TIMESTAMP WHERE id = ?`,
          values: [nextTrxNo, updatedBy, txn.id]
        }))
      };

      const result = {
        success: true,
        message: `Test calculation completed for ${validTransactions.length} transactions`,
        testData: {
          // Input parameters
          input: {
            detail_ids: detailIds,
            updated_by: updatedBy,
            running_date: today,
            using_mock_data: !!mockTransactions
          },
          
          // Transaction analysis
          transaction_analysis: {
            total_requested: detailIds.length,
            found_in_system: transactionDetails.rows.length,
            valid_pending: validTransactions.length,
            already_transferred: transactionDetails.rows.filter(txn => txn.is_transfer === 1).length
          },
          
          // Generated transaction number
          transaction_number: nextTrxNo,
          
          // Financial calculations
          financial_totals: {
            sum_amount: Number(totals.sum_amount.toFixed(2)),
            net_amount: Number(totals.net_amount.toFixed(2)),
            vat_amount: Number(totals.vat_amount.toFixed(2)),
            withhold_amount: Number(totals.withhold_amount.toFixed(2)),
            mdr_amount: Number(totals.mdr_amount.toFixed(2)),
            bank_bbl_amount: Number(totals.bank_bbl_amount.toFixed(2)),
            bank_oth_amount: Number(totals.bank_oth_amount.toFixed(2)),
            bank_bbl_count: totals.bank_bbl_count,
            bank_oth_count: totals.bank_oth_count,
            transfer_fee_bbl: Number(totals.transfer_fee_bbl.toFixed(2)),
            transfer_fee_oth: Number(totals.transfer_fee_oth.toFixed(2)),
            mdr_amount_bbl: Number(totals.mdr_amount_bbl.toFixed(2)),
            mdr_amount_oth: Number(totals.mdr_amount_oth.toFixed(2)),
            vat_amount_bbl: Number(totals.vat_amount_bbl.toFixed(2)),
            vat_amount_oth: Number(totals.vat_amount_oth.toFixed(2)),
            deduction_total: Number((totals.sum_amount - totals.net_amount).toFixed(2)),
            deduction_percentage: Number(((totals.sum_amount - totals.net_amount) / totals.sum_amount * 100).toFixed(2))
          },
          
          // Merchant breakdown
          merchant_breakdown: Object.entries(merchantBreakdown).map(([vat, data]: [string, any]) => ({
            merchant_vat: vat,
            merchant_name: data.merchant_name,
            transaction_count: data.transaction_count,
            total_amount: Number(data.total_amount.toFixed(2)),
            net_amount: Number(data.net_amount.toFixed(2)),
            vat_amount: Number(data.vat_amount.toFixed(2)),
            withhold_tax: Number(data.withhold_tax.toFixed(2)),
            mdr_amount: Number(data.mdr_amount.toFixed(2)),
            bank_refs: Array.from(data.bank_refs)
          })),
          
          // Validation results
          validation: {
            passed: !hasErrors,
            results: validationResults,
            summary: {
              total_checks: validationResults.length,
              passed: validationResults.filter(v => v.status === 'success').length,
              warnings: validationResults.filter(v => v.status === 'warning').length,
              errors: validationResults.filter(v => v.status === 'error').length
            }
          },
          
          // SQL preview
          sql_preview: sqlPreview,
          
          // Transaction details
          transaction_details: validTransactions.map(txn => ({
            id: txn.id,
            merchant_vat: txn.merchant_vat,
            merchant_name: txn.merchant_name,
            transaction_date: txn.transaction_date,
            total_amount: Number(txn.total_amount),
            net_amount: Number(txn.net_amount),
            current_transfer_status: txn.is_transfer,
            bank_ref: txn.bank_ref
          }))
        }
      };

      console.log('🎉 Test calculation completed successfully');
      console.log(`📊 Totals: Sum=${totals.sum_amount}, Net=${totals.net_amount}, Validation=${!hasErrors ? 'PASSED' : 'FAILED'}`);

      return result;

    } catch (error) {
      console.error('❌ Error in test enhanced workflow calculations:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Test calculation failed',
        testData: null
      };
    }
  });

  // Search transaction payment form logs with pagination and filtering
  ipcMain.handle('search-transaction-payment-forms', async (_event, params: TransactionPaymentFormSearchParams = {}): Promise<TransactionPaymentFormSearchResponse> => {
    try {
      const {
        page = 1,
        pageSize = 20,
        search = '',
        startDate,
        endDate,
        trxNo,
        createdBy,
        sortBy = 'create_dt',
        sortOrder = 'DESC'
      } = params;

      console.log('🔍 Searching transaction payment forms with params:', params);

      let whereConditions = [];
      let queryParams: any[] = [];
      let paramIndex = 1;

      // Text search across multiple fields
      if (search) {
        whereConditions.push(`(
          trx_no ILIKE $${paramIndex} OR
          create_by ILIKE $${paramIndex} OR
          update_by ILIKE $${paramIndex}
        )`);
        queryParams.push(`%${search}%`);
        paramIndex++;
      }

      // Date range filter
      if (startDate) {
        whereConditions.push(`DATE(create_dt) >= $${paramIndex}`);
        queryParams.push(startDate);
        paramIndex++;
      }

      if (endDate) {
        whereConditions.push(`DATE(create_dt) <= $${paramIndex}`);
        queryParams.push(endDate);
        paramIndex++;
      }

      // Specific transaction number filter
      if (trxNo) {
        whereConditions.push(`trx_no ILIKE $${paramIndex}`);
        queryParams.push(`%${trxNo}%`);
        paramIndex++;
      }

      // Created by filter
      if (createdBy) {
        whereConditions.push(`create_by ILIKE $${paramIndex}`);
        queryParams.push(`%${createdBy}%`);
        paramIndex++;
      }

      const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

      // Get total count
      const countResult = await executeQuery(`
        SELECT COUNT(*) as total 
        FROM transaction_payment_form 
        ${whereClause}
      `, queryParams);
      const totalRecords = parseInt(countResult.rows[0].total);

      // Calculate pagination
      const offset = (page - 1) * pageSize;
      const totalPages = Math.ceil(totalRecords / pageSize);

      // Validate sort column
      const validSortColumns = [
        'id', 'trx_no', 'sum_amount', 'net_amount', 'bank_bbl_amount', 'bank_oth_amount',
        'vat_amount', 'withhold_amount', 'mdr_amount', 'approve_dt', 'create_dt', 'update_dt',
        'create_by', 'update_by', 'active'
      ];
      const sortColumn = validSortColumns.includes(sortBy) ? sortBy : 'create_dt';
      const sortDirection = sortOrder === 'ASC' ? 'ASC' : 'DESC';

      // Get paginated results
      const dataResult = await executeQuery(`
        SELECT
          id,
          trx_no,
          bank_bbl_amount,
          bank_oth_amount,
          sum_amount,
          vat_amount,
          withhold_amount,
          mdr_amount,
          net_amount,
          approve_dt,
          transfer_dt_bbl,
          transfer_dt_oth,
          active,
          create_by,
          create_dt,
          update_by,
          update_dt
        FROM transaction_payment_form
        ${whereClause}
        ORDER BY ${sortColumn} ${sortDirection}
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `, [...queryParams, pageSize, offset]);

      console.log(`✅ Found ${dataResult.rows.length} transaction payment forms (Page ${page}/${totalPages})`);

      return {
        success: true,
        data: dataResult.rows,
        pagination: {
          currentPage: page,
          totalPages,
          totalRecords,
          pageSize,
          hasNextPage: page < totalPages,
          hasPreviousPage: page > 1
        },
        message: `Found ${totalRecords} transaction payment form records`
      };

    } catch (error) {
      console.error('❌ Error searching transaction payment forms:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to search transaction payment forms'
      };
    }
  });

  // Get transaction payment form details with related transaction summary details
  ipcMain.handle('get-transaction-payment-form-details', async (_event, trxNo: string): Promise<TransactionPaymentFormDetailsResponse> => {
    try {
      console.log(`🔍 Getting payment form details for transaction: ${trxNo}`);

      // Get payment form data
      const paymentFormResult = await executeQuery(`
        SELECT
          id,
          trx_no,
          bank_bbl_amount,
          bank_oth_amount,
          sum_amount,
          vat_amount,
          withhold_amount,
          mdr_amount,
          net_amount,
          approve_dt,
          transfer_dt_bbl,
          transfer_dt_oth,
          bank_bbl_count,
          bank_oth_count,
          transfer_fee_bbl,
          transfer_fee_oth,
          mdr_amount_bbl,
          mdr_amount_oth,
          vat_amount_bbl,
          vat_amount_oth,
          active,
          create_by,
          create_dt,
          update_by,
          update_dt
        FROM transaction_payment_form
        WHERE trx_no = $1
      `, [trxNo]);

      if (paymentFormResult.rows.length === 0) {
        return {
          success: false,
          error: `No payment form found for transaction number: ${trxNo}`
        };
      }

      // Get related transaction summary details
      const transactionDetailsResult = await executeQuery(`
        SELECT
          id,
          merchant_vat,
          merchant_name,
          transaction_date,
          channel_type,
          total_amount,
          final_net_amount,
          vat_amount,
          withhold_tax,
          mdr_amount,
          transfer_fee,
          is_transfer,
          bank_ref,
          create_dt,
          update_dt
        FROM transaction_summary_report_detail
        WHERE trx_no = $1
        ORDER BY merchant_vat, transaction_date
      `, [trxNo]);

      console.log(`✅ Found payment form with ${transactionDetailsResult.rows.length} related transactions`);

      return {
        success: true,
        data: {
          paymentForm: paymentFormResult.rows[0],
          transactionDetails: transactionDetailsResult.rows,
          summary: {
            totalTransactions: transactionDetailsResult.rows.length,
            totalMerchants: new Set(transactionDetailsResult.rows.map(t => t.merchant_vat)).size,
            totalChannels: new Set(transactionDetailsResult.rows.map(t => t.channel_type)).size
          }
        }
      };

    } catch (error) {
      console.error('❌ Error getting transaction payment form details:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get payment form details'
      };
    }
  });

  // Get transaction payment form details with paginated related transactions
  ipcMain.handle('get-transaction-payment-form-details-paginated', async (_event, params: {
    trxNo: string;
    page?: number;
    pageSize?: number;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
  }): Promise<TransactionPaymentFormDetailsResponse & { pagination?: PaginationInfo }> => {
    try {
      const { trxNo, page = 1, pageSize = 10, sortBy = 'transaction_date', sortOrder = 'DESC' } = params;

      console.log(`🔍 Getting paginated payment form details for transaction: ${trxNo}, page: ${page}, pageSize: ${pageSize}`);

      // Get payment form data (same as before)
      const paymentFormResult = await executeQuery(`
        SELECT
          id,
          trx_no,
          bank_bbl_amount,
          bank_oth_amount,
          sum_amount,
          vat_amount,
          withhold_amount,
          mdr_amount,
          net_amount,
          approve_dt,
          transfer_dt_bbl,
          transfer_dt_oth,
          bank_bbl_count,
          bank_oth_count,
          transfer_fee_bbl,
          transfer_fee_oth,
          mdr_amount_bbl,
          mdr_amount_oth,
          vat_amount_bbl,
          vat_amount_oth,
          active,
          create_by,
          create_dt,
          update_by,
          update_dt
        FROM transaction_payment_form
        WHERE trx_no = $1
      `, [trxNo]);

      if (paymentFormResult.rows.length === 0) {
        return {
          success: false,
          error: `No payment form found for transaction number: ${trxNo}`
        };
      }

      // Get total count of related transactions
      const countResult = await executeQuery(`
        SELECT COUNT(*) as total
        FROM transaction_summary_report_detail
        WHERE trx_no = $1
      `, [trxNo]);

      const totalRecords = parseInt(countResult.rows[0].total);
      const totalPages = Math.ceil(totalRecords / pageSize);
      const offset = (page - 1) * pageSize;

      // Validate sort column
      const validSortColumns = ['transaction_date', 'merchant_vat', 'merchant_name', 'channel_type', 'total_amount', 'bank_ref'];
      const sortColumn = validSortColumns.includes(sortBy) ? sortBy : 'transaction_date';
      const sortDirection = sortOrder === 'ASC' ? 'ASC' : 'DESC';

      // Get paginated related transaction summary details
      const transactionDetailsResult = await executeQuery(`
        SELECT
          id,
          merchant_vat,
          merchant_name,
          transaction_date,
          channel_type,
          total_amount,
          final_net_amount,
          vat_amount,
          withhold_tax,
          mdr_amount,
          transfer_fee,
          is_transfer,
          bank_ref,
          create_dt,
          update_dt,
          trade_status
        FROM transaction_summary_report_detail
        WHERE trx_no = $1
        ORDER BY ${sortColumn} ${sortDirection}, merchant_vat
        LIMIT $2 OFFSET $3
      `, [trxNo, pageSize, offset]);

      // Get summary data (total counts without pagination)
      const summaryResult = await executeQuery(`
        SELECT
          COUNT(*) as total_transactions,
          COUNT(DISTINCT merchant_vat) as total_merchants,
          COUNT(DISTINCT channel_type) as total_channels
        FROM transaction_summary_report_detail
        WHERE trx_no = $1
      `, [trxNo]);

      const summary = summaryResult.rows[0];

      console.log(`✅ Found payment form with ${transactionDetailsResult.rows.length} related transactions (page ${page}/${totalPages})`);

      return {
        success: true,
        data: {
          paymentForm: paymentFormResult.rows[0],
          transactionDetails: transactionDetailsResult.rows,
          summary: {
            totalTransactions: parseInt(summary.total_transactions),
            totalMerchants: parseInt(summary.total_merchants),
            totalChannels: parseInt(summary.total_channels)
          }
        },
        pagination: {
          currentPage: page,
          totalPages,
          totalRecords,
          pageSize,
          hasNextPage: page < totalPages,
          hasPreviousPage: page > 1
        }
      };

    } catch (error) {
      console.error('❌ Error getting paginated transaction payment form details:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get paginated payment form details'
      };
    }
  });

  // Get transaction summary report data by trx_no from transaction_summary_report_detail table
  ipcMain.handle('get-transaction-summary-report-by-trx-no', async (_event, trxNo: string): Promise<{
    success: boolean;
    data?: any;
    error?: string;
  }> => {
    try {
      console.log(`📊 Getting transaction summary report data for trx_no: ${trxNo}`);

      // Get transaction summary details from transaction_summary_report_detail table
      const summaryDetailsResult = await executeQuery(`
        SELECT 
          d.*,
          m.merchant_name,
          m.transfer_fee,
          m.withholding_tax,
          mw.wechat_rate as mdr_rate,
          ns.vat_percentage,
          b.bank_code,
          b.bank_ref,
          b.bank_name_th,
          b.bank_name_en
        FROM transaction_summary_report_detail d
        LEFT JOIN merchant m ON d.merchant_vat = m.merchant_vat
        LEFT JOIN merchant_wechat mw ON m.merchant_id = mw.merchant_id AND mw.active = true
        LEFT JOIN network_service ns ON ns.active = true
        LEFT JOIN tmst_bank b ON d.bank_ref = b.bank_ref
        WHERE d.trx_no = $1
        ORDER BY d.transaction_date, d.merchant_vat, d.channel_type
      `, [trxNo]);

      if (summaryDetailsResult.rows.length === 0) {
        return {
          success: false,
          error: `No transaction summary data found for trx_no: ${trxNo}`
        };
      }

      console.log(`📊 Found ${summaryDetailsResult.rows.length} transaction summary records for trx_no: ${trxNo}`);

      // Transform data to match ReportSummary interface
      const merchants = summaryDetailsResult.rows.map(row => ({
        id: row.id, // Primary key from transaction_summary_report_detail
        merchantVat: row.merchant_vat,
        merchantName: row.merchant_name || 'Unknown Merchant',
        transactionDate: row.transaction_date,
        transactionCount: parseInt(row.transaction_count) || 1,
        totalAmount: parseFloat(row.total_amount) || 0,
        mdrRate: parseFloat(row.mdr_rate) || 0,
        mdrAmount: parseFloat(row.mdr_amount) || 0,
        vatPercentage: parseFloat(row.vat_percentage) || 0,
        vatAmount: parseFloat(row.vat_amount) || 0,
        netAmount: parseFloat(row.net_amount) || 0,
        withholdingTaxRate: parseFloat(row.withholding_tax_rate || row.withholding_tax) || 0,
        withholdTax: parseFloat(row.withhold_tax) || 0,
        transferFee: parseFloat(row.transfer_fee) || 0,
        reimbursementFee: parseFloat(row.reimbursement_fee) || 0,
        serviceFee: parseFloat(row.service_fee) || 0,
        finalNetAmount: parseFloat(row.final_net_amount) || 0,
        cupBusinessTaxFee: parseFloat(row.cup_business_tax_fee) || 0,
        channelType: row.channel_type,
        tradeStatus: (row.trade_status || 'SUCCESS') as 'SUCCESS' | 'REFUND' | 'ADJUST',
        bankCode: row.bank_ref || 'UNKNOWN',
        bankNameTh: row.bank_name_th || 'Unknown Bank',
        bankNameEn: row.bank_name_en || 'Unknown Bank',
        // Additional fields from transaction_summary_report_detail table
        bankRef: row.bank_ref || 'UNKNOWN', // Bank reference from the detail table
        trxNo: row.trx_no || null, // Transaction number (set when approved)
        isTransfer: row.is_transfer || 0, // Transfer status (0 = pending, 1 = transferred)
        batchId: row.batch_id || null, // Batch ID for grouping
        reportDate: row.report_date || null, // Report date
        reportTime: row.report_time || null, // Report time
        runningNumber: row.running_number || null, // Running number
        status: row.status || 'GENERATED', // Report status
        createBy: row.create_by || 'SYSTEM', // Created by user
        createDt: row.create_dt || null, // Creation timestamp
        updateBy: row.update_by || null, // Updated by user
        updateDt: row.update_dt || null // Update timestamp
      }));

      // Calculate grand totals
      const grandTotals = {
        totalTransactions: merchants.reduce((sum, m) => sum + m.transactionCount, 0),
        totalAmount: merchants.reduce((sum, m) => sum + m.totalAmount, 0),
        totalMdrAmount: merchants.reduce((sum, m) => sum + m.mdrAmount, 0),
        totalVatAmount: merchants.reduce((sum, m) => sum + m.vatAmount, 0),
        totalNetAmount: merchants.reduce((sum, m) => sum + m.netAmount, 0),
        totalWithholdTax: merchants.reduce((sum, m) => sum + m.withholdTax, 0),
        totalTransferFee: merchants.reduce((sum, m) => sum + m.transferFee, 0),
        totalReimbursementFee: merchants.reduce((sum, m) => sum + m.reimbursementFee, 0),
        totalServiceFee: merchants.reduce((sum, m) => sum + m.serviceFee, 0),
        totalFinalNetAmount: merchants.reduce((sum, m) => sum + m.finalNetAmount, 0),
        totalCupBusinessTaxFee: merchants.reduce((sum, m) => sum + m.cupBusinessTaxFee, 0),
        averageMdrRate: merchants.length > 0 ? merchants.reduce((sum, m) => sum + m.mdrRate, 0) / merchants.length : 0,
        averageVatPercentage: merchants.length > 0 ? merchants.reduce((sum, m) => sum + m.vatPercentage, 0) / merchants.length : 0,
        averageWithholdingTaxRate: merchants.length > 0 ? merchants.reduce((sum, m) => sum + m.withholdingTaxRate, 0) / merchants.length : 0
      };

      // Generate bank subtotals
      const bankGroups = new Map<string, any[]>();
      merchants.forEach(merchant => {
        const bankCode = merchant.bankCode || 'UNKNOWN';
        if (!bankGroups.has(bankCode)) {
          bankGroups.set(bankCode, []);
        }
        bankGroups.get(bankCode)!.push(merchant);
      });

      const bankSubtotals = Array.from(bankGroups.entries()).map(([bankCode, bankMerchants]) => {
        const firstMerchant = bankMerchants[0];
        return {
          bankCode,
          bankNameTh: firstMerchant.bankNameTh,
          bankNameEn: firstMerchant.bankNameEn,
          merchants: bankMerchants,
          subtotals: {
            totalTransactions: bankMerchants.reduce((sum, m) => sum + m.transactionCount, 0),
            totalAmount: bankMerchants.reduce((sum, m) => sum + m.totalAmount, 0),
            totalMdrAmount: bankMerchants.reduce((sum, m) => sum + m.mdrAmount, 0),
            totalVatAmount: bankMerchants.reduce((sum, m) => sum + m.vatAmount, 0),
            totalNetAmount: bankMerchants.reduce((sum, m) => sum + m.netAmount, 0),
            totalWithholdTax: bankMerchants.reduce((sum, m) => sum + m.withholdTax, 0),
            totalTransferFee: bankMerchants.reduce((sum, m) => sum + m.transferFee, 0),
            totalReimbursementFee: bankMerchants.reduce((sum, m) => sum + m.reimbursementFee, 0),
            totalServiceFee: bankMerchants.reduce((sum, m) => sum + m.serviceFee, 0),
            totalFinalNetAmount: bankMerchants.reduce((sum, m) => sum + m.finalNetAmount, 0),
            totalCupBusinessTaxFee: bankMerchants.reduce((sum, m) => sum + m.cupBusinessTaxFee, 0)
          }
        };
      });

      // Create report data structure
      const reportData = {
        merchants,
        bankSubtotals,
        grandTotals,
        reportDate: new Date().toLocaleDateString('en-GB'),
        reportTime: new Date().toLocaleTimeString('en-GB'),
        pageCount: 1
      };

      return {
        success: true,
        data: reportData
      };

    } catch (error) {
      console.error('❌ Error getting transaction summary report by trx_no:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get transaction summary report'
      };
    }
  });

  // Create transaction adjustment request
  ipcMain.handle('create-transaction-adjustment-request', async (_event, adjustmentData: {
    disputed_amount: number;
    disputed_full?: number;
    disputed_type?: string;
    currency_code?: string;
    support_document?: boolean;
    cdrs?: string;
    card_type?: string;
    reason?: string;
    transaction_ref: string;
    transaction_id: string;
    merchant_vat?: string;
    merchant_id?: string;
    transaction_channel_type?: string;
    transaction_transaction_time?: string;
    create_by: string;
  }): Promise<{ success: boolean; message: string; data?: any; error?: string }> => {
    try {
      // Use the service to create the adjustment request
      const result = await TransactionAdjustmentService.createAdjustmentRequest(adjustmentData);
      return result;
    } catch (error: any) {
      console.error('❌ Error creating transaction adjustment request:', error);
      return {
        success: false,
        message: 'Failed to create transaction adjustment request',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // Get transaction adjustment requests with pagination
  ipcMain.handle('get-transaction-adjustments-paginated', async (_event, params: {
    page?: number;
    pageSize?: number;
    search?: string;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
    startDate?: string;
    endDate?: string;
    disputedType?: string;
    transactionRef?: string;
    createBy?: string;
  }) => {
    try {
      console.log('🔍 Getting paginated transaction adjustments with params:', params);

      const {
        page = 1,
        pageSize = 10,
        search = '',
        sortBy = 'create_dt',
        sortOrder = 'DESC',
        startDate,
        endDate,
        disputedType,
        transactionRef,
        createBy
      } = params;

      // Validate page and pageSize
      const validPage = Math.max(1, page);
      const validPageSize = Math.min(Math.max(1, pageSize), 100); // Max 100 per page
      const offset = (validPage - 1) * validPageSize;

      // Build WHERE conditions
      const conditions: string[] = ['active = true'];
      const values: any[] = [];
      let paramIndex = 1;

      // Date range filter
      if (startDate) {
        conditions.push(`create_dt >= $${paramIndex}::date`);
        values.push(startDate);
        paramIndex++;
      }

      if (endDate) {
        conditions.push(`create_dt <= $${paramIndex}::date + interval '1 day'`);
        values.push(endDate);
        paramIndex++;
      }

      // Dispute type filter
      if (disputedType) {
        conditions.push(`disputed_type = $${paramIndex}`);
        values.push(disputedType);
        paramIndex++;
      }

      // Transaction reference filter
      if (transactionRef) {
        conditions.push(`transaction_ref ILIKE $${paramIndex}`);
        values.push(`%${transactionRef}%`);
        paramIndex++;
      }

      // Created by filter
      if (createBy) {
        conditions.push(`create_by ILIKE $${paramIndex}`);
        values.push(`%${createBy}%`);
        paramIndex++;
      }

      // General search filter
      if (search) {
        conditions.push(`(
          transaction_ref ILIKE $${paramIndex} OR
          reason ILIKE $${paramIndex} OR
          disputed_type ILIKE $${paramIndex} OR
          create_by ILIKE $${paramIndex}
        )`);
        values.push(`%${search}%`);
        paramIndex++;
      }

      const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

      // Validate sortBy to prevent SQL injection
      const allowedSortColumns = [
        'id', 'disputed_amount', 'disputed_type', 'transaction_ref',
        'create_by', 'create_dt', 'update_dt'
      ];
      const validSortBy = allowedSortColumns.includes(sortBy) ? sortBy : 'create_dt';
      const validSortOrder = sortOrder === 'ASC' ? 'ASC' : 'DESC';

      // Get total count
      const countQuery = `
        SELECT COUNT(*) as total
        FROM transaction_request_adjust
        ${whereClause}
      `;

      const countResult = await executeQuery(countQuery, values);
      const totalRecords = parseInt(countResult.rows[0].total);
      const totalPages = Math.ceil(totalRecords / validPageSize);

      // Get paginated data
      const dataQuery = `
        SELECT
          id,
          disputed_amount,
          disputed_full,
          disputed_type,
          currency_code,
          support_document,
          cdrs,
          card_type,
          reason,
          transaction_ref,
          transaction_report_detail_id,
          active,
          create_by,
          create_dt,
          update_by,
          update_dt
        FROM transaction_request_adjust
        ${whereClause}
        ORDER BY ${validSortBy} ${validSortOrder}
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;

      const dataValues = [...values, validPageSize, offset];
      const dataResult = await executeQuery(dataQuery, dataValues);

      const pagination = {
        currentPage: validPage,
        totalPages,
        totalRecords,
        pageSize: validPageSize,
        hasNextPage: validPage < totalPages,
        hasPreviousPage: validPage > 1
      };

      console.log(`✅ Retrieved ${dataResult.rows.length} adjustment requests (page ${validPage}/${totalPages})`);

      return {
        success: true,
        data: dataResult.rows,
        pagination,
        message: `Retrieved ${dataResult.rows.length} adjustment requests`
      };

    } catch (error) {
      console.error('❌ Error getting paginated adjustment requests:', error);
      return {
        success: false,
        message: 'Failed to get adjustment requests',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // Get dispute form data for PDF generation
  ipcMain.handle('get-dispute-form-data', async (_event, adjustmentId: number) => {
    try {
      console.log('📄 Getting dispute form data for adjustment ID:', adjustmentId);

      // 1. Get company settings (institution information)
      const companyQuery = `
        SELECT
          company_name_en,
          company_name_th,
          company_address_en,
          company_address_th,
          company_phone,
          company_fax,
          company_email,
          company_contact_person
        FROM company_setting
        WHERE active = true
        LIMIT 1
      `;
      const companyResult = await executeQuery(companyQuery);
      const companyData = companyResult.rows[0] || {};

      // 2. Get adjustment request data
      const adjustmentQuery = `
        SELECT
          id,
          disputed_amount,
          disputed_full,
          disputed_type,
          currency_code,
          support_document,
          cdrs,
          card_type,
          reason,
          transaction_ref,
          create_by,
          create_dt
        FROM transaction_request_adjust
        WHERE id = $1 AND active = true
      `;
      const adjustmentResult = await executeQuery(adjustmentQuery, [adjustmentId]);

      if (adjustmentResult.rows.length === 0) {
        return {
          success: false,
          message: 'Adjustment request not found'
        };
      }

      const adjustmentData = adjustmentResult.rows[0];

      // 3. Get transaction information using transaction_ref
      const transactionQuery = `
        SELECT
          transaction_id,
          transaction_amount,
          transaction_merchant_id,
          transaction_merchant_name,
          transaction_merchant_vat,
          transaction_card_no,
          transaction_trade_type,
          transaction_channel_type,
          transaction_time,
          reference_no
        FROM transaction_e_pos
        WHERE transaction_id = $1
        LIMIT 1
      `;
      const transactionResult = await executeQuery(transactionQuery, [adjustmentData.transaction_ref]);
      const transactionData = transactionResult.rows[0] || {};

      // 4. Format the data for DisputeFormData interface
      const disputeFormData = {
        // Request metadata
        requestDate: new Date(adjustmentData.create_dt).toLocaleDateString('th-TH', {
          day: '2-digit',
          month: '2-digit',
          year: '2-digit'
        }),
        requestId: adjustmentData.id.toString(),

        // Institution information (from company_setting)
        institutionName: companyData.company_name_en || "E-POS Service Company Limited",
        iin: "30340764", // Fixed IIN for the company
        contactName: companyData.company_contact_person || "Admin User",
        phone: companyData.company_phone || "(662) 8215459",
        fax: companyData.company_fax || "",
        email: companyData.company_email || "<EMAIL>",
        title: "Mr." as const,

        // Transaction information (from transaction_e_pos)
        cardNumber: transactionData.transaction_card_no || "",
        transactionDate: transactionData.transaction_time ?
          new Date(transactionData.transaction_time).toLocaleDateString('th-TH') : "",
        transactionTime: transactionData.transaction_time ?
          new Date(transactionData.transaction_time).toLocaleTimeString('th-TH') : "",
        merchantId: transactionData.transaction_merchant_id || "",
        transactionAmount: transactionData.transaction_amount ?
          transactionData.transaction_amount.toLocaleString('th-TH', { minimumFractionDigits: 2 }) : "0.00",
        transactionType: transactionData.transaction_trade_type || "",
        referenceNumber: transactionData.reference_no || "",
        terminalId: "33173516", // Fixed terminal ID
        currencyCode: adjustmentData.currency_code || "THB",
        merchantName: transactionData.transaction_merchant_name || "",

        // Dispute information (from transaction_request_adjust)
        disputedAmount: adjustmentData.disputed_amount ?
          adjustmentData.disputed_amount.toLocaleString('th-TH', { minimumFractionDigits: 2 }) : "0.00",
        amountType: adjustmentData.disputed_full === 1 ? "full" as const : "partial" as const,
        disputeType: adjustmentData.disputed_type ? [adjustmentData.disputed_type] : ["Credit Adjustment"],
        reasonCode: "",
        supportingDocs: adjustmentData.support_document ? "yes" as const : "no" as const,
        pages: "1",
        additionalMessage: adjustmentData.reason || "Refund diff amount to customer."
      };

      console.log('✅ Successfully prepared dispute form data');

      return {
        success: true,
        data: disputeFormData
      };

    } catch (error) {
      console.error('❌ Error getting dispute form data:', error);
      return {
        success: false,
        message: 'Failed to get dispute form data',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  console.log('✅ Transaction handlers registered successfully');
}
