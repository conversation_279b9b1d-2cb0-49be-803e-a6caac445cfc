import { Client } from 'pg';
import { getDbConnection } from '../db';

export interface MerchantFinancialData {
  merchant_id: string;
  merchant_vat: string;
  merchant_name: string;
  transfer_fee: number;
  withholding_tax: number;
  mdr_rate: number;
  vat_percentage: number;
  bank_ref?: string;
}

export interface FinancialCalculation {
  disputedAmount: number;
  mdrRate: number;
  mdrAmount: number;
  vatPercentage: number;
  vatAmount: number;
  netAmount: number;
  withholdingTaxRate: number;
  withholdTax: number;
  transferFee: number;
  reimbursementFee: number;
  serviceFee: number;
  cupBusinessTaxFee: number;
  finalNetAmount: number;
}

export interface AdjustmentRequestData {
  disputed_amount: number;
  disputed_full?: number;
  disputed_type?: string;
  currency_code?: string;
  support_document?: boolean;
  cdrs?: string;
  card_type?: string;
  reason?: string;
  transaction_ref: string;
  transaction_id: string;
  merchant_vat?: string;
  merchant_id?: string;
  transaction_channel_type?: string;
  transaction_transaction_time?: string;
  create_by: string;
}

export class TransactionAdjustmentService {
  /**
   * Get merchant financial data including MDR rate and VAT percentage
   */
  static async getMerchantFinancialData(
    client: Client,
    merchantVat?: string
  ): Promise<MerchantFinancialData> {
    console.log('🔍 Looking up merchant by VAT:', merchantVat);

    if (!merchantVat) {
      throw new Error('Merchant VAT is required for adjustment request');
    }

    const query = `
      SELECT
        m.merchant_id,
        m.merchant_vat,
        m.merchant_name,
        m.transfer_fee,
        m.withholding_tax,
        mw.wechat_rate as mdr_rate,
        ns.vat_percentage,
        b.bank_ref
      FROM merchant m
      LEFT JOIN merchant_wechat mw ON m.merchant_id = mw.merchant_id AND mw.active = true
      LEFT JOIN network_service ns ON ns.active = true
      LEFT JOIN merchant_bank mb ON m.merchant_id = mb.merchant_id AND mb.active = true
      LEFT JOIN tmst_bank b ON mb.bank_id = b.bank_id AND b.active = true
      WHERE m.merchant_vat = $1
      LIMIT 1
    `;

    const result = await client.query(query, [merchantVat]);

    if (result.rows.length === 0) {
      throw new Error('Merchant not found for adjustment request');
    }

    const row = result.rows[0];
    return {
      merchant_id: row.merchant_id,
      merchant_vat: row.merchant_vat,
      merchant_name: row.merchant_name,
      transfer_fee: parseFloat(row.transfer_fee) || 0,
      withholding_tax: parseFloat(row.withholding_tax) || 0,
      mdr_rate: parseFloat(row.mdr_rate) || 0,
      vat_percentage: parseFloat(row.vat_percentage) || 0,
      bank_ref: row.bank_ref || 'ADJ_REF'
    };
  }

  /**
   * Calculate financial values for adjustment request
   */
  static calculateFinancials(
    disputedAmount: number,
    merchantData: MerchantFinancialData
  ): FinancialCalculation {
    const mdrAmount = disputedAmount * (merchantData.mdr_rate / 100);
    const vatAmount = mdrAmount * (merchantData.vat_percentage / 100);
    const netAmount = disputedAmount - mdrAmount;
    const withholdTax = netAmount * (merchantData.withholding_tax / 100);
    
    // Set fees to 0 as specified in requirements
    const reimbursementFee = 0;
    const serviceFee = 0;
    const cupBusinessTaxFee = 0;
    
    const finalNetAmount = 0;

    return {
      disputedAmount,
      mdrRate: merchantData.mdr_rate,
      mdrAmount,
      vatPercentage: merchantData.vat_percentage,
      vatAmount,
      netAmount,
      withholdingTaxRate: merchantData.withholding_tax,
      withholdTax,
      transferFee: merchantData.transfer_fee,
      reimbursementFee,
      serviceFee,
      cupBusinessTaxFee,
      finalNetAmount
    };
  }

  /**
   * Insert financial calculation into transaction_summary_report_detail
   */
  static async insertReportDetail(
    client: Client,
    merchantData: MerchantFinancialData,
    calculation: FinancialCalculation,
    channelType: string,
    transactionDate: string,
    createBy: string
  ): Promise<string> {
    const query = `
      INSERT INTO transaction_summary_report_detail (
        merchant_vat,
        merchant_name,
        transaction_date,
        channel_type,
        transaction_count,
        total_amount,
        mdr_rate,
        mdr_amount,
        vat_percentage,
        vat_amount,
        net_amount,
        withholding_tax_rate,
        withhold_tax,
        transfer_fee,
        reimbursement_fee,
        service_fee,
        cup_business_tax_fee,
        final_net_amount,
        trade_status,
        trx_no,
        bank_ref,
        create_by
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22
      ) RETURNING id
    `;

    const values = [
      merchantData.merchant_vat,
      merchantData.merchant_name,
      transactionDate, // Transaction date from original transaction
      channelType, // Channel type from original transaction
      1, // Always 1 transaction for adjustments
      -Math.abs(calculation.disputedAmount), // Negative amount for adjustments
      calculation.mdrRate,
      -Math.abs(calculation.mdrAmount), // Negative MDR amount for adjustments
      calculation.vatPercentage,
      -Math.abs(calculation.vatAmount), // Negative VAT amount for adjustments
      -Math.abs(calculation.netAmount), // Negative net amount for adjustments
      calculation.withholdingTaxRate,
      -Math.abs(calculation.withholdTax), // Negative withholding tax for adjustments
      calculation.transferFee, // Keep transfer fee positive (it's a cost)
      calculation.reimbursementFee, // Keep reimbursement fee positive (it's a cost)
      calculation.serviceFee, // Keep service fee positive (it's a cost)
      calculation.cupBusinessTaxFee, // Keep business tax fee positive (it's a cost)
      calculation.finalNetAmount, // Final net amount is not used in adjustments, set to 0
      'ADJUST', // Trade status always 'adjust'
      null, // trx_no - set to null for adjustments
      merchantData.bank_ref || 'ADJ_REF', // bank_ref - use merchant's bank_ref or default
      createBy
    ];

    const result = await client.query(query, values);
    return result.rows[0].id;
  }

  /**
   * Insert adjustment request record
   */
  static async insertAdjustmentRequest(
    client: Client,
    adjustmentData: AdjustmentRequestData,
    reportDetailId: string
  ): Promise<any> {
    const query = `
      INSERT INTO transaction_request_adjust (
        disputed_amount,
        disputed_full,
        disputed_type,
        currency_code,
        support_document,
        cdrs,
        card_type,
        reason,
        transaction_ref,
        transaction_report_detail_id,
        create_by
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11
      ) RETURNING id, disputed_amount, reason, transaction_ref, transaction_report_detail_id, create_dt
    `;

    const values = [
      Math.abs(adjustmentData.disputed_amount), // Store as positive value in adjustment request table
      adjustmentData.disputed_full || 0,
      adjustmentData.disputed_type || null,
      adjustmentData.currency_code || 'THB',
      adjustmentData.support_document || false,
      adjustmentData.cdrs || null,
      adjustmentData.card_type || null,
      adjustmentData.reason || 'Refund diff amount to customer.',
      adjustmentData.transaction_ref,
      reportDetailId,
      adjustmentData.create_by
    ];

    const result = await client.query(query, values);
    return result.rows[0];
  }

  /**
   * Create complete adjustment request with financial calculations
   */
  static async createAdjustmentRequest(
    adjustmentData: AdjustmentRequestData
  ): Promise<{
    success: boolean;
    message: string;
    data?: {
      adjustmentRequest: any;
      financialCalculation: FinancialCalculation;
    };
    error?: string;
  }> {
    let client: Client | null = null;

    try {
      console.log('➕ Creating transaction adjustment request for transaction:', adjustmentData.transaction_id);
      console.log('📊 Adjustment data:', JSON.stringify(adjustmentData, null, 2));
      client = await getDbConnection();
      console.log('✅ Database connection established');

      // Start transaction
      await client.query('BEGIN');

      // Get merchant financial data
      console.log('🔍 Getting merchant financial data for:', adjustmentData.merchant_vat, adjustmentData.merchant_id);
      const merchantData = await this.getMerchantFinancialData(
        client,
        adjustmentData.merchant_vat
      );
      console.log('✅ Merchant data retrieved:', JSON.stringify(merchantData, null, 2));

      // Calculate financial values
      const calculation = this.calculateFinancials(
        adjustmentData.disputed_amount,
        merchantData
      );

      // Insert into transaction_summary_report_detail
      // Format transaction date for database (YYYY-MM-DD format)
      const transactionDate = adjustmentData.transaction_transaction_time
        ? new Date(adjustmentData.transaction_transaction_time).toISOString().split('T')[0]
        : new Date().toISOString().split('T')[0]; // Fallback to current date

      console.log('📅 Using transaction date:', transactionDate, 'from original time:', adjustmentData.transaction_transaction_time);
      console.log('💰 Financial calculations for adjustment:', {
        originalAmount: calculation.disputedAmount,
        reportDetailAmount: -Math.abs(calculation.disputedAmount), // Negative in report detail
        adjustmentRequestAmount: Math.abs(calculation.disputedAmount), // Positive in adjustment request
        originalMDR: calculation.mdrAmount,
        adjustmentMDR: -Math.abs(calculation.mdrAmount)
      });

      const reportDetailId = await this.insertReportDetail(
        client,
        merchantData,
        calculation,
        adjustmentData.transaction_channel_type || 'adjust',
        transactionDate,
        adjustmentData.create_by
      );

      // Insert adjustment request
      const adjustmentRequest = await this.insertAdjustmentRequest(
        client,
        adjustmentData,
        reportDetailId
      );

      // Commit transaction
      await client.query('COMMIT');

      console.log('✅ Transaction adjustment request created successfully');

      return {
        success: true,
        message: 'Transaction adjustment request created successfully',
        data: {
          adjustmentRequest,
          financialCalculation: calculation
        }
      };

    } catch (error: any) {
      // Rollback transaction on error
      if (client) {
        try {
          await client.query('ROLLBACK');
        } catch (rollbackError) {
          console.error('❌ Error rolling back transaction:', rollbackError);
        }
      }

      console.error('❌ Error creating transaction adjustment request:', error);
      console.error('❌ Error stack:', error.stack);
      return {
        success: false,
        message: 'Failed to create transaction adjustment request',
        error: error.message || error.toString()
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  }
}
