// Monthly Backup Configuration and Utilities

export interface MonthlyBackupConfig {
  // Base folder structure
  baseFolder: string;
  
  // File naming conventions
  fileNaming: {
    includeTimestamp: boolean;
    timestampFormat: 'ISO' | 'YYYYMMDD' | 'YYYYMMDD_HHMMSS';
    prefix?: string;
    suffix?: string;
  };
  
  // Backup retention policy
  retention: {
    keepMonths: number;
    autoCleanup: boolean;
    cleanupSchedule?: 'monthly' | 'weekly';
  };
  
  // Folder organization
  folderStructure: {
    format: 'YYYY-MM' | 'YYYY/MM' | 'YYYY-MM-DD';
    createSubfolders: boolean;
    subfoldersByType?: boolean; // e.g., /csv_backup/2025-08/transactions/, /csv_backup/2025-08/reports/
  };
  
  // Metadata options
  metadata: {
    generateMetadata: boolean;
    includeFileHashes: boolean;
    includeSystemInfo: boolean;
    customFields?: Record<string, any>;
  };
  
  // Upload options
  upload: {
    batchSize: number;
    delayBetweenUploads: number; // milliseconds
    retryAttempts: number;
    retryDelay: number; // milliseconds
  };
  
  // Notifications
  notifications: {
    onSuccess: boolean;
    onError: boolean;
    includeStats: boolean;
  };
}

export const DEFAULT_MONTHLY_BACKUP_CONFIG: MonthlyBackupConfig = {
  baseFolder: '/csv_backup',
  
  fileNaming: {
    includeTimestamp: true,
    timestampFormat: 'YYYYMMDD',
    prefix: undefined,
    suffix: undefined
  },
  
  retention: {
    keepMonths: 12,
    autoCleanup: false,
    cleanupSchedule: 'monthly'
  },
  
  folderStructure: {
    format: 'YYYY-MM',
    createSubfolders: false,
    subfoldersByType: false
  },
  
  metadata: {
    generateMetadata: true,
    includeFileHashes: false,
    includeSystemInfo: true,
    customFields: {}
  },
  
  upload: {
    batchSize: 10,
    delayBetweenUploads: 500,
    retryAttempts: 3,
    retryDelay: 1000
  },
  
  notifications: {
    onSuccess: true,
    onError: true,
    includeStats: true
  }
};

/**
 * Utility class for monthly backup operations
 */
export class MonthlyBackupUtils {
  
  /**
   * Generate folder path based on configuration
   */
  static generateFolderPath(
    config: MonthlyBackupConfig,
    date: Date = new Date()
  ): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    let datePath: string;
    
    switch (config.folderStructure.format) {
      case 'YYYY-MM':
        datePath = `${year}-${month}`;
        break;
      case 'YYYY/MM':
        datePath = `${year}/${month}`;
        break;
      case 'YYYY-MM-DD':
        datePath = `${year}-${month}-${day}`;
        break;
      default:
        datePath = `${year}-${month}`;
    }
    
    return `${config.baseFolder}/${datePath}`;
  }
  
  /**
   * Generate timestamped filename
   */
  static generateFileName(
    originalFileName: string,
    config: MonthlyBackupConfig,
    date: Date = new Date()
  ): string {
    if (!config.fileNaming.includeTimestamp) {
      return originalFileName;
    }
    
    const ext = originalFileName.split('.').pop();
    const baseName = originalFileName.replace(`.${ext}`, '');
    
    let timestamp: string;
    
    switch (config.fileNaming.timestampFormat) {
      case 'ISO':
        timestamp = date.toISOString().replace(/[:.]/g, '-');
        break;
      case 'YYYYMMDD':
        timestamp = date.toISOString().split('T')[0].replace(/-/g, '');
        break;
      case 'YYYYMMDD_HHMMSS':
        const isoString = date.toISOString();
        const datePart = isoString.split('T')[0].replace(/-/g, '');
        const timePart = isoString.split('T')[1].split('.')[0].replace(/:/g, '');
        timestamp = `${datePart}_${timePart}`;
        break;
      default:
        timestamp = date.toISOString().split('T')[0].replace(/-/g, '');
    }
    
    const prefix = config.fileNaming.prefix ? `${config.fileNaming.prefix}_` : '';
    const suffix = config.fileNaming.suffix ? `_${config.fileNaming.suffix}` : '';
    
    return `${prefix}${baseName}_${timestamp}${suffix}.${ext}`;
  }
  
  /**
   * Get months to keep based on retention policy
   */
  static getMonthsToKeep(
    config: MonthlyBackupConfig,
    currentDate: Date = new Date()
  ): { year: number; month: number }[] {
    const months: { year: number; month: number }[] = [];
    
    for (let i = 0; i < config.retention.keepMonths; i++) {
      const date = new Date(currentDate);
      date.setMonth(date.getMonth() - i);
      
      months.push({
        year: date.getFullYear(),
        month: date.getMonth() + 1
      });
    }
    
    return months;
  }
  
  /**
   * Get months to cleanup (older than retention period)
   */
  static getMonthsToCleanup(
    config: MonthlyBackupConfig,
    allMonths: { year: number; month: number }[],
    currentDate: Date = new Date()
  ): { year: number; month: number }[] {
    const monthsToKeep = this.getMonthsToKeep(config, currentDate);
    const keepSet = new Set(
      monthsToKeep.map(m => `${m.year}-${String(m.month).padStart(2, '0')}`)
    );
    
    return allMonths.filter(
      m => !keepSet.has(`${m.year}-${String(m.month).padStart(2, '0')}`)
    );
  }
  
  /**
   * Generate backup metadata
   */
  static generateBackupMetadata(
    config: MonthlyBackupConfig,
    filePaths: string[],
    backupDate: Date = new Date()
  ): any {
    const metadata: any = {
      version: '1.0',
      timestamp: backupDate.toISOString(),
      backupType: 'monthly',
      configuration: {
        folderFormat: config.folderStructure.format,
        retention: config.retention.keepMonths
      },
      files: {
        total: filePaths.length,
        list: filePaths.map(fp => ({
          originalPath: fp,
          fileName: require('path').basename(fp)
        }))
      }
    };
    
    if (config.metadata.includeSystemInfo) {
      metadata.system = {
        platform: process.platform,
        nodeVersion: process.version,
        timestamp: Date.now()
      };
    }
    
    if (config.metadata.customFields) {
      metadata.custom = { ...config.metadata.customFields };
    }
    
    return metadata;
  }
  
  /**
   * Validate backup configuration
   */
  static validateConfig(config: MonthlyBackupConfig): {
    valid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    // Validate base folder
    if (!config.baseFolder || config.baseFolder.trim() === '') {
      errors.push('Base folder cannot be empty');
    }
    
    // Validate retention policy
    if (config.retention.keepMonths < 1) {
      errors.push('Retention period must be at least 1 month');
    }
    
    if (config.retention.keepMonths > 60) {
      warnings.push('Retention period longer than 5 years may consume significant storage');
    }
    
    // Validate upload settings
    if (config.upload.batchSize < 1) {
      errors.push('Batch size must be at least 1');
    }
    
    if (config.upload.delayBetweenUploads < 0) {
      errors.push('Delay between uploads cannot be negative');
    }
    
    // Validate folder structure
    const validFormats = ['YYYY-MM', 'YYYY/MM', 'YYYY-MM-DD'];
    if (!validFormats.includes(config.folderStructure.format)) {
      errors.push(`Invalid folder format. Must be one of: ${validFormats.join(', ')}`);
    }
    
    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }
  
  /**
   * Create a backup summary report
   */
  static createBackupSummary(
    results: any[],
    config: MonthlyBackupConfig,
    duration: number
  ): string {
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    const totalSize = results
      .filter(r => r.success && r.size)
      .reduce((sum, r) => sum + r.size, 0);
    
    const summary = `
📊 Monthly Backup Summary
========================
📅 Date: ${new Date().toISOString()}
📁 Folder: ${config.baseFolder}
📈 Results: ${successful} successful, ${failed} failed
📦 Total Size: ${(totalSize / 1024 / 1024).toFixed(2)} MB
⏱️ Duration: ${(duration / 1000).toFixed(2)} seconds
⚙️ Configuration: ${config.folderStructure.format} format, ${config.retention.keepMonths} months retention

${results.length > 0 ? '📄 Files:' : ''}
${results.map(r => `  ${r.success ? '✅' : '❌'} ${r.fileName || 'Unknown'}`).join('\n')}
`;
    
    return summary.trim();
  }
}

/**
 * Type-safe configuration builder
 */
export class MonthlyBackupConfigBuilder {
  private config: MonthlyBackupConfig;
  
  constructor(baseConfig: Partial<MonthlyBackupConfig> = {}) {
    this.config = { ...DEFAULT_MONTHLY_BACKUP_CONFIG, ...baseConfig };
  }
  
  setBaseFolder(folder: string): this {
    this.config.baseFolder = folder;
    return this;
  }
  
  setRetention(months: number, autoCleanup: boolean = false): this {
    this.config.retention.keepMonths = months;
    this.config.retention.autoCleanup = autoCleanup;
    return this;
  }
  
  setFolderFormat(format: 'YYYY-MM' | 'YYYY/MM' | 'YYYY-MM-DD'): this {
    this.config.folderStructure.format = format;
    return this;
  }
  
  enableTimestamps(format: 'ISO' | 'YYYYMMDD' | 'YYYYMMDD_HHMMSS' = 'YYYYMMDD'): this {
    this.config.fileNaming.includeTimestamp = true;
    this.config.fileNaming.timestampFormat = format;
    return this;
  }
  
  disableTimestamps(): this {
    this.config.fileNaming.includeTimestamp = false;
    return this;
  }
  
  setBatchSize(size: number, delay: number = 500): this {
    this.config.upload.batchSize = size;
    this.config.upload.delayBetweenUploads = delay;
    return this;
  }
  
  enableMetadata(includeHashes: boolean = false, includeSystem: boolean = true): this {
    this.config.metadata.generateMetadata = true;
    this.config.metadata.includeFileHashes = includeHashes;
    this.config.metadata.includeSystemInfo = includeSystem;
    return this;
  }
  
  addCustomMetadata(fields: Record<string, any>): this {
    this.config.metadata.customFields = { ...this.config.metadata.customFields, ...fields };
    return this;
  }
  
  build(): MonthlyBackupConfig {
    const validation = MonthlyBackupUtils.validateConfig(this.config);
    
    if (!validation.valid) {
      throw new Error(`Invalid configuration: ${validation.errors.join(', ')}`);
    }
    
    if (validation.warnings.length > 0) {
      console.warn('Configuration warnings:', validation.warnings.join(', '));
    }
    
    return { ...this.config };
  }
}
