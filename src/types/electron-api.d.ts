// Global type definitions for Electron API

declare global {
  interface Window {
    electronAPI?: {
      invoke: (channel: string, ...args: any[]) => Promise<any>;
      logout: () => Promise<boolean>;
      searchTransactionPaymentForms: (params: any) => Promise<any>;
      getTransactionPaymentFormDetails: (trxNo: string) => Promise<any>;
      getTransactionPaymentFormDetailsPaginated: (params: {
        trxNo: string;
        page?: number;
        pageSize?: number;
        sortBy?: string;
        sortOrder?: 'ASC' | 'DESC';
      }) => Promise<any>;
      getTransactionSummaryReportByTrxNo: (trxNo: string) => Promise<any>;
      getCompanySettings: () => Promise<any>;
    };
  }
}

export {};
