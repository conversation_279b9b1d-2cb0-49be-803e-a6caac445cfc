# Transaction Summary Report Excel Export - User Guide

## How to Export Transaction Summary Reports to Excel

### Step 1: Access Transaction Log Search
1. Navigate to the **Transaction Log Search** modal
2. Search and find the transaction payment form you want to analyze
3. Click the **"View"** button for any transaction to open details

### Step 2: Generate Transaction Summary Report
1. In the transaction details modal, click the **"Summary Report"** button (📊 icon)
2. Wait for the report generation process to complete
3. The Transaction Summary Report viewer will open

### Step 3: Export to Excel
1. In the Transaction Summary Report viewer, you will see two export options:
   - **"Download PDF"** button (📄 icon) - for PDF export (existing)
   - **"Export Excel"** button (📊 icon) - for Excel export (**NEW**)

2. Click the **"Export Excel"** button
3. A file save dialog will open
4. Choose your desired save location
5. The filename will be auto-generated as: `transaction-summary-report-YYYY-MM-DD.xlsx`
6. Click **"Save"** to save the Excel file
7. A success message will confirm the export completion

## Excel File Contents

The exported Excel file contains **identical data** to the PDF report with the following sections:

### Header Information
- Report title and date
- Report generation time
- Total number of merchants
- Total number of transactions

### Merchant Transaction Details
Each row contains:
- **Transaction Date**: Date of the transaction
- **Merchant Name**: Name of the merchant (with "(Refund)" for refund transactions)
- **MDR %**: Merchant Discount Rate percentage
- **Transaction Count**: Number of transactions
- **Transaction Amount**: Total transaction amount
- **MDR Amount**: Calculated MDR fees
- **Transfer Fee**: Bank transfer fees
- **VAT Amount**: Value Added Tax amount
- **Withholding Tax**: Tax withheld from merchant
- **Net Amount**: Amount after deductions
- **Reimburse Fee**: Reimbursement fees
- **Service Fee**: Service charges
- **Business Tax**: CUP business tax
- **Final Net Amount**: Final amount after all deductions
- **Channel Type**: Payment channel (WeChat, AliPay, etc.)

### Bank Subtotals (if applicable)
- Grouped totals by bank (BBL, Other Banks)
- Subtotal calculations for each bank group

### Grand Totals
- Overall totals for all financial metrics
- Average rates and percentages

## Excel Benefits vs PDF

### ✅ **Advantages of Excel Export:**
1. **Data Analysis**: Perform calculations, create formulas
2. **Sorting & Filtering**: Advanced data manipulation
3. **Charts & Graphs**: Create custom visualizations
4. **Integration**: Import into other systems
5. **Customization**: Modify formatting and layout
6. **Pivot Tables**: Advanced data summarization
7. **Multiple Formats**: Save as CSV, XML, etc.

### 📊 **Use Cases for Excel Export:**
1. **Financial Analysis**: Detailed revenue and fee analysis
2. **Reporting**: Custom reports for management
3. **Reconciliation**: Compare with bank statements
4. **Budgeting**: Include in financial planning
5. **Auditing**: Detailed transaction audits
6. **Tax Preparation**: Support for tax filings

## Troubleshooting

### If Export Button is Disabled:
- Ensure the transaction summary report has loaded completely
- Check that you have report data available
- Wait for any ongoing report generation to complete

### If Export Fails:
1. **Check Permissions**: Ensure you have write permissions to the save location
2. **Free Space**: Verify sufficient disk space
3. **File Access**: Close the Excel file if it's already open
4. **Retry**: Try saving to a different location

### If Excel File Won't Open:
1. **Excel Version**: Ensure you have Excel 2007 or newer (supports .xlsx)
2. **Alternative Apps**: Try Google Sheets, LibreOffice Calc
3. **File Integrity**: Re-export if file appears corrupted

### Common Error Messages:
- **"Failed to save Excel file"**: Check file permissions and disk space
- **"Save dialog failed"**: Try restarting the application
- **"Export failed"**: Check your internet connection and try again

## Best Practices

### File Management:
1. **Naming**: Use descriptive names for easy identification
2. **Organization**: Create folders by date or merchant
3. **Backup**: Keep copies of important reports
4. **Version Control**: Include dates in filenames

### Data Analysis:
1. **Freeze Panes**: Freeze header rows for easier scrolling
2. **Conditional Formatting**: Highlight important data
3. **Data Validation**: Ensure data integrity during analysis
4. **Protection**: Protect important formulas and data

## Technical Notes

### File Format:
- **Format**: Excel 2007+ (.xlsx)
- **Compatibility**: Works with Excel, Google Sheets, LibreOffice
- **Size**: Optimized for reasonable file sizes
- **Encoding**: UTF-8 for international characters

### Data Precision:
- **Currency**: 2 decimal places for all amounts
- **Percentages**: 2-4 decimal places for rates
- **Dates**: Standard date formatting
- **Numbers**: Proper numeric formatting for calculations

## Support

If you encounter any issues with the Excel export functionality:

1. **Check Documentation**: Review this guide and error messages
2. **System Requirements**: Ensure your system meets requirements
3. **Contact Support**: Report persistent issues to IT support
4. **Alternative**: Use PDF export as fallback option

---

**Note**: The Excel export feature maintains complete data consistency with PDF reports, ensuring that all financial calculations and totals match exactly.
