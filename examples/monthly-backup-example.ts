import { PCloudService } from '../src/main/services/pcloudService';

// Example usage of enhanced PCloudService with YYYY-MM date organization

async function demonstrateMonthlyBackup() {
  // Initialize PCloud service
  const pcloudService = new PCloudService({
    username: '<EMAIL>',
    password: 'your-password',
    region: 'us' // or 'eu'
  });

  try {
    // Test connection first
    console.log('🔗 Testing pCloud connection...');
    const connectionTest = await pcloudService.testConnection();
    
    if (!connectionTest.success) {
      console.error('❌ Connection failed:', connectionTest.message);
      return;
    }

    console.log('✅ Connected to pCloud successfully');
    console.log('📊 Account info:', connectionTest.userInfo);

    // Example 1: Upload single file with current month folder (2025-08)
    console.log('\n📤 Example 1: Single file upload with date folder');
    const singleFileResult = await pcloudService.uploadFileWithDateFolder(
      '/path/to/your/data.csv',
      '/csv_backup'
    );
    
    if (singleFileResult.success) {
      console.log('✅ File uploaded to:', singleFileResult.remotePath);
    } else {
      console.error('❌ Upload failed:', singleFileResult.error);
    }

    // Example 2: Batch upload multiple files
    console.log('\n📤 Example 2: Batch upload to monthly folder');
    const filesToUpload = [
      '/path/to/file1.csv',
      '/path/to/file2.csv',
      '/path/to/file3.csv'
    ];

    const batchResult = await pcloudService.uploadFilesWithDateFolders(
      filesToUpload,
      '/csv_backup'
    );

    console.log(`📊 Batch upload results: ${batchResult.successCount} success, ${batchResult.failureCount} failed`);

    // Example 3: Create structured backup with metadata
    console.log('\n📦 Example 3: Structured backup with metadata');
    const structuredBackup = await pcloudService.createStructuredBackup(
      filesToUpload,
      '/csv_backup',
      {
        source: 'Daily Transaction Export',
        description: 'Automated backup of transaction data',
        tags: ['transactions', 'daily', 'automated']
      }
    );

    if (structuredBackup.success) {
      console.log('✅ Structured backup created in:', structuredBackup.backupPath);
      console.log('📄 Metadata file uploaded:', structuredBackup.metadataFile?.success);
    }

    // Example 4: List files from a specific month
    console.log('\n📂 Example 4: List monthly backups');
    const monthlyFiles = await pcloudService.listMonthlyBackups(
      '/csv_backup',
      2025,
      8 // August
    );

    if (monthlyFiles.success && monthlyFiles.files) {
      console.log(`📁 Found ${monthlyFiles.files.length} files in ${monthlyFiles.folderPath}`);
      monthlyFiles.files.forEach((file: any) => {
        if (!file.isfolder) {
          console.log(`  📄 ${file.name} (${file.size} bytes)`);
        }
      });
    }

    // Example 5: Get backup statistics for current month
    console.log('\n📊 Example 5: Monthly backup statistics');
    const stats = await pcloudService.getMonthlyBackupStats(
      '/csv_backup',
      2025,
      8 // August
    );

    if (stats.success && stats.stats) {
      console.log('📈 Monthly Statistics:');
      console.log(`  Total Files: ${stats.stats.totalFiles}`);
      console.log(`  CSV Files: ${stats.stats.csvFiles}`);
      console.log(`  Total Size: ${(stats.stats.totalSize / 1024 / 1024).toFixed(2)} MB`);
      console.log(`  Oldest File: ${stats.stats.oldestFile}`);
      console.log(`  Newest File: ${stats.stats.newestFile}`);
    }

    // Example 6: Upload to specific month (not current month)
    console.log('\n📅 Example 6: Upload to specific month folder');
    const specificDate = new Date('2025-07-15'); // July 2025
    const specificMonthResult = await pcloudService.uploadFileWithDateFolder(
      '/path/to/historical-data.csv',
      '/csv_backup',
      specificDate
    );

    if (specificMonthResult.success) {
      console.log('✅ Historical file uploaded to:', specificMonthResult.remotePath);
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Additional utility functions for backup management

/**
 * Generate backup report for multiple months
 */
async function generateBackupReport(
  pcloudService: PCloudService,
  baseFolder: string = '/csv_backup',
  months: { year: number; month: number }[]
) {
  console.log('📊 Generating backup report for multiple months...');
  
  const report = {
    totalMonths: months.length,
    totalFiles: 0,
    totalSize: 0,
    totalCsvFiles: 0,
    monthlyBreakdown: [] as any[]
  };

  for (const { year, month } of months) {
    const stats = await pcloudService.getMonthlyBackupStats(baseFolder, year, month);
    
    if (stats.success && stats.stats) {
      report.totalFiles += stats.stats.totalFiles;
      report.totalSize += stats.stats.totalSize;
      report.totalCsvFiles += stats.stats.csvFiles;
      
      report.monthlyBreakdown.push({
        month: `${year}-${String(month).padStart(2, '0')}`,
        ...stats.stats
      });
    }
  }

  console.log('📈 Backup Report Summary:');
  console.log(`  Months Analyzed: ${report.totalMonths}`);
  console.log(`  Total Files: ${report.totalFiles}`);
  console.log(`  Total CSV Files: ${report.totalCsvFiles}`);
  console.log(`  Total Size: ${(report.totalSize / 1024 / 1024).toFixed(2)} MB`);
  
  return report;
}

/**
 * Automated monthly backup scheduler helper
 */
class MonthlyBackupScheduler {
  private pcloudService: PCloudService;
  
  constructor(pcloudService: PCloudService) {
    this.pcloudService = pcloudService;
  }

  /**
   * Schedule backup for specific files every month
   */
  async scheduleMonthlyBackup(
    filePaths: string[],
    baseFolder: string = '/csv_backup',
    metadata?: any
  ) {
    console.log('🗓️ Executing scheduled monthly backup...');
    
    return await this.pcloudService.createStructuredBackup(
      filePaths,
      baseFolder,
      {
        ...metadata,
        source: 'Scheduled Monthly Backup',
        scheduledAt: new Date().toISOString()
      }
    );
  }

  /**
   * Cleanup old backups (keep last N months)
   */
  async cleanupOldBackups(
    baseFolder: string = '/csv_backup',
    keepMonths: number = 6
  ) {
    console.log(`🧹 Cleanup functionality would remove backups older than ${keepMonths} months`);
    console.log('⚠️ Note: Implement deletion logic based on your retention policy');
    
    // This is a placeholder - implement actual deletion logic based on your needs
    // You would need to:
    // 1. List all month folders in baseFolder
    // 2. Identify folders older than keepMonths
    // 3. Use pCloud API to delete old folders
    // 4. Be very careful with deletion operations!
  }
}

// Export for use in your application
export { demonstrateMonthlyBackup, generateBackupReport, MonthlyBackupScheduler };

// Run demonstration if this file is executed directly
if (require.main === module) {
  demonstrateMonthlyBackup().catch(console.error);
}
