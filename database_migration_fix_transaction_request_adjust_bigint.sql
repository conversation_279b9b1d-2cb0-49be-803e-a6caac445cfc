-- Migration: Fix transaction_request_adjust table data types
-- Description: Change transaction_report_detail_id from INTEGER to BIGINT to match transaction_summary_report_detail.id
-- Date: 2025-08-07

-- Check if the table exists and show current structure
SELECT 
    'Current transaction_request_adjust structure:' as info,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'transaction_request_adjust' 
ORDER BY ordinal_position;

-- Drop the foreign key constraint first (if it exists)
ALTER TABLE transaction_request_adjust 
DROP CONSTRAINT IF EXISTS transaction_request_adjust_transaction_report_detail_id_fkey;

-- Change the column type from INTEGER to BIGINT
ALTER TABLE transaction_request_adjust 
ALTER COLUMN transaction_report_detail_id TYPE BIGINT;

-- Re-add the foreign key constraint
ALTER TABLE transaction_request_adjust 
ADD CONSTRAINT transaction_request_adjust_transaction_report_detail_id_fkey 
FOREIGN KEY (transaction_report_detail_id) 
REFERENCES transaction_summary_report_detail(id);

-- Verify the change
SELECT 
    'Updated transaction_request_adjust structure:' as info,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'transaction_request_adjust' 
ORDER BY ordinal_position;
